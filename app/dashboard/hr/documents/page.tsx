'use client';

import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import DocumentManagement from '@/components/documents/DocumentManagement';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { generateBreadcrumbs } from '@/lib/breadcrumb-utils';
import { usePathname } from 'next/navigation';

export default function DocumentsPage() {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, 'hr');

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <Breadcrumb items={breadcrumbs} />
        <DocumentManagement />
      </div>
    </DashboardLayout>
  );
}
