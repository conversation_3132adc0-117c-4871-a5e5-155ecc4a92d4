import { BreadcrumbItem } from "@/components/ui/Breadcrumb";

// Define route mappings for breadcrumb generation
const routeLabels: Record<string, string> = {
  dashboard: "Dashboard",
  hr: "HR",
  admin: "Admin",
  manager: "Manager",
  employee: "Employee",
  "super-admin": "Super Admin",
  attendance: "Attendance",
  daily: "Daily Report",
  statistics: "Statistics",
  leave: "Leave",
  analytics: "Analytics",
  trends: "Trends",
  employees: "Employees",
  departments: "Departments",
  announcements: "Announcements",
  shifts: "Shifts",
  documents: "Documents",
  payroll: "Payroll",
  reports: "Reports",
  settings: "Settings",
  profile: "Profile",
  companies: "Companies",
  countries: "Countries",
  subscriptions: "Subscriptions",
  "audit-logs": "Audit Logs",
  new: "New",
  edit: "Edit",
  view: "View",
};

// Special cases for dynamic routes
const dynamicRouteLabels: Record<string, (id: string) => string> = {
  employee: (id: string) => `Employee ${id}`,
  company: (id: string) => `Company ${id}`,
  department: (id: string) => `Department ${id}`,
};

/**
 * Generate breadcrumb items from a pathname
 * @param pathname - The current pathname (e.g., '/dashboard/hr/attendance/daily')
 * @param userRole - The user's role to determine the correct dashboard base
 * @returns Array of breadcrumb items
 */
export function generateBreadcrumbs(
  pathname: string,
  userRole?: string
): BreadcrumbItem[] {
  const segments = pathname.split("/").filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  // Always start with home/dashboard
  if (segments.length === 0) {
    return [{ label: "Dashboard", href: "/", isActive: true }];
  }

  // Build breadcrumbs progressively
  let currentPath = "";

  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;

    // Get label for this segment
    let label = routeLabels[segment] || segment;

    // Handle special cases
    if (segment === "dashboard" && index === 0) {
      // Skip the first 'dashboard' segment as it's redundant
      return;
    }

    // Handle role-specific dashboard paths
    if (index === 1 && segments[0] === "dashboard") {
      const roleSegment = segments[1];
      if (
        ["hr", "admin", "manager", "employee", "super-admin"].includes(
          roleSegment
        )
      ) {
        label = "Dashboard";
        currentPath = getDashboardPath(roleSegment);
      }
    }

    // Handle attendance sub-pages
    if (segment === "dashboard" && segments[index - 1] === "attendance") {
      label = "Statistics";
    }

    // Handle employee sub-pages in attendance
    if (segment === "employee" && segments[index - 1] === "attendance") {
      label = "Employee Stats";
    }

    // Check if this is a dynamic route (UUID-like)
    if (isUUID(segment)) {
      const parentSegment = segments[index - 1];
      if (dynamicRouteLabels[parentSegment]) {
        label = dynamicRouteLabels[parentSegment](segment);
      } else {
        label = `${routeLabels[parentSegment] || parentSegment} Details`;
      }
    }

    breadcrumbs.push({
      label,
      href: isLast ? undefined : currentPath,
      isActive: isLast,
    });
  });

  return breadcrumbs;
}

/**
 * Get the dashboard path for a specific role
 */
function getDashboardPath(role: string): string {
  const roleMap: Record<string, string> = {
    hr: "/dashboard/hr",
    admin: "/dashboard/admin",
    manager: "/dashboard/manager",
    employee: "/dashboard/employee",
    "super-admin": "/dashboard/super-admin",
  };

  return roleMap[role] || "/dashboard";
}

/**
 * Check if a string looks like a UUID
 */
function isUUID(str: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str) || str.length > 20; // Also catch other long IDs
}

/**
 * Check if a path is active (current path starts with the given path)
 * This is used for sidebar navigation active states
 */
export function isPathActive(currentPath: string, itemPath: string): boolean {
  // Exact match
  if (currentPath === itemPath) {
    return true;
  }

  // Check if current path starts with item path (for sub-pages)
  // But avoid false positives like '/dashboard/hr' matching '/dashboard/hr-other'
  if (currentPath.startsWith(itemPath + "/")) {
    return true;
  }

  return false;
}
