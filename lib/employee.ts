// lib/employee.ts

import { apiGet } from "./api";
import { getAccessToken, getCompanyCountryCode } from "./auth";
import { EmployeeType } from "@/types/payroll";

// Employee interface for basic employee data
interface Employee {
  employee_id: string;
  full_name: string;
  email?: string;
  phone_number?: string;
  department_id?: string;
  position?: string;
}

interface EmployeeResponse {
  extend: {
    employees: Employee[];
    pagination?: {
      has_next: boolean;
      page: number;
      pages: number;
      per_page: number;
      total_count: number;
    };
  };
  msg: string;
  success: boolean;
}

/**
 * Get employee types for the current company's country
 * @returns Promise with employee types array
 */
export async function getEmployeeTypesForCompany(): Promise<EmployeeType[]> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const countryCode = getCompanyCountryCode();
  if (!countryCode) {
    throw new Error("Company country code not found");
  }

  try {
    // Convert country code to lowercase as required by the API
    const lowercaseCountryCode = countryCode.toLowerCase();

    const response = await apiGet<{
      employee_types: EmployeeType[];
      country: { code: string; name: string };
      success: boolean;
      total_count?: number;
    }>(`api/countries/${lowercaseCountryCode}/employee-types`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response.employee_types || [];
  } catch (error) {
    // Return empty array if employee types are not available (for attendance-only clients)
    return [];
  }
}

/**
 * Get a specific employee type by ID
 * @param employeeTypeId The employee type ID
 * @returns Promise with employee type or null if not found
 */
export async function getEmployeeTypeById(
  employeeTypeId: string
): Promise<EmployeeType | null> {
  const employeeTypes = await getEmployeeTypesForCompany();
  return (
    employeeTypes.find((type) => type.employee_type_id === employeeTypeId) ||
    null
  );
}

/**
 * Check if employee types are available for the current company
 * @returns Promise with boolean indicating if employee types are available
 */
export async function areEmployeeTypesAvailable(): Promise<boolean> {
  try {
    const employeeTypes = await getEmployeeTypesForCompany();
    return employeeTypes.length > 0;
  } catch (error) {
    // If there's an error fetching employee types, assume they're not available
    return false;
  }
}

/**
 * Get all employees for a company (with pagination)
 * @param companyId The company ID
 * @returns Promise with all employees
 */
export async function getEmployees(
  companyId: string
): Promise<{ employee_id: string; full_name: string }[]> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    let allEmployees: { employee_id: string; full_name: string }[] = [];
    let currentPage = 1;
    let hasNextPage = true;

    // Fetch all pages of employees
    while (hasNextPage) {
      const response = await apiGet<EmployeeResponse>(
        `api/employees?company_id=${companyId}&page=${currentPage}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.extend && response.extend.employees) {
        // Map to the format expected by the document upload form
        const mappedEmployees = response.extend.employees.map((emp) => ({
          employee_id: emp.employee_id,
          full_name: emp.full_name,
        }));

        allEmployees = [...allEmployees, ...mappedEmployees];

        // Check if there are more pages
        if (response.extend.pagination) {
          hasNextPage = response.extend.pagination.has_next;
          currentPage++;
        } else {
          // If no pagination info, assume this is the last page
          hasNextPage = false;
        }
      } else {
        hasNextPage = false;
      }
    }

    return allEmployees;
  } catch (error) {
    throw error;
  }
}
