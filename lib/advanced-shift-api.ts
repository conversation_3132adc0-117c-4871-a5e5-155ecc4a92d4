/**
 * Advanced Shift Management API Service
 * Handles all API calls for advanced scheduling functionality
 */

import { apiGet, apiPost } from './api';
import { getAccessToken } from './auth';
import {
  TemplateExamplesResponse,
  AvailableTemplatesResponse,
  TemplateAnalysisResponse,
  CreateTemplateRequest,
  CreateTemplateResponse,
  ValidateRequirementsRequest,
  ValidateRequirementsResponse,
  GenerateScheduleRequest,
  GenerateScheduleResponse,
  AssignScheduleRequest,
  AssignScheduleResponse,
  AvailableShiftsResponse
} from '@/types/advanced-shift';

/**
 * Get scheduling template examples
 */
export async function getSchedulingTemplateExamples(): Promise<TemplateExamplesResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiGet<TemplateExamplesResponse>('api/scheduling/examples/business-requirements', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Get available scheduling templates for a company
 */
export async function getAvailableTemplates(companyId: string): Promise<AvailableTemplatesResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiGet<AvailableTemplatesResponse>(`api/scheduling/templates/business-friendly?company_id=${companyId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Analyze a specific template
 */
export async function analyzeTemplate(templateId: string, companyId: string): Promise<TemplateAnalysisResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiGet<TemplateAnalysisResponse>(`api/scheduling/template-analysis/${templateId}?company_id=${companyId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Get available shifts for template creation (using existing shifts API)
 */
export async function getAvailableShifts(companyId: string): Promise<AvailableShiftsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Use the existing shifts API endpoint that works
    const response = await apiGet<any>(`api/shifts?company_id=${companyId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    // Extract shifts from the response (handle both nested and direct structure)
    let shifts = [];
    if (response.extend && response.extend.shifts) {
      shifts = response.extend.shifts;
    } else if (response.shifts) {
      shifts = response.shifts;
    } else {
      shifts = [];
    }

    // Transform the shifts to match the expected AvailableShift interface
    const availableShifts = shifts.map((shift: any) => ({
      shift_id: shift.shift_id,
      name: shift.name,
      description: shift.description || '',
      start_time: shift.start_time,
      end_time: shift.end_time,
      is_night_shift: shift.is_night_shift || false,
      is_flexible: shift.is_flexible || false,
      working_days: shift.working_days || '1,2,3,4,5'
    }));

    return {
      shifts: availableShifts,
      message: 'Available shifts retrieved successfully'
    };
  } catch (error: any) {
    throw new Error(`Error getting available shifts: ${error.message}`);
  }
}

/**
 * Validate business requirements before creating template
 */
export async function validateBusinessRequirements(data: ValidateRequirementsRequest): Promise<ValidateRequirementsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiPost<ValidateRequirementsResponse>('api/scheduling/validate-requirements', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Create a new structured scheduling template
 */
export async function createSchedulingTemplate(data: CreateTemplateRequest): Promise<CreateTemplateResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiPost<CreateTemplateResponse>('api/scheduling/create-structured-template', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Generate a schedule from a template
 */
export async function generateSchedule(data: GenerateScheduleRequest): Promise<GenerateScheduleResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiPost<GenerateScheduleResponse>('api/scheduling/generate-schedule', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Assign a schedule to employees or departments
 */
export async function assignSchedule(data: AssignScheduleRequest): Promise<AssignScheduleResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiPost<AssignScheduleResponse>('api/scheduling/assign-schedule', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Helper function to format days of week for display
 */
export function formatDaysOfWeek(days: number[]): string {
  const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  return days.map(day => dayNames[day - 1]).join(', ');
}

/**
 * Helper function to format time range
 */
export function formatTimeRange(startTime: string, endTime: string): string {
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours % 12 || 12;
    return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
}

/**
 * Helper function to calculate shift duration in hours
 */
export function calculateShiftDuration(startTime: string, endTime: string): number {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);
  
  const startTotalMinutes = startHours * 60 + startMinutes;
  let endTotalMinutes = endHours * 60 + endMinutes;
  
  // Handle overnight shifts
  if (endTotalMinutes <= startTotalMinutes) {
    endTotalMinutes += 24 * 60;
  }
  
  return (endTotalMinutes - startTotalMinutes) / 60;
}

/**
 * Helper function to get weekend requirement display text
 */
export function getWeekendRequirementText(requirement: string): string {
  switch (requirement) {
    case 'flexible':
      return 'Flexible';
    case 'both_off':
      return 'Both Days Off';
    case 'at_least_one_off':
      return 'At Least One Day Off';
    case 'both_required':
      return 'Both Days Required';
    default:
      return requirement;
  }
}

/**
 * Helper function to validate shift time format
 */
export function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

/**
 * Helper function to get coverage plan display text
 */
export function getCoveragePlanText(plan: string): string {
  switch (plan) {
    case 'mandatory_overtime':
      return 'Mandatory Overtime';
    case 'call_in_overtime':
      return 'Call-in Overtime';
    case 'temporary_staff':
      return 'Temporary Staff';
    case 'reduce_service':
      return 'Reduce Service';
    default:
      return plan;
  }
}
