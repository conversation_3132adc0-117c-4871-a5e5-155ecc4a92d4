/**
 * Document Management API Service
 * Handles all API calls for document management functionality
 */

import { apiGet, createApiUrl } from "./api";
import { getAccessToken } from "./auth";
import {
  Document,
  DocumentUploadRequest,
  DocumentUploadResponse,
  DocumentListResponse,
  DocumentDetailsResponse,
  ExpiringDocumentsResponse,
  DocumentFilters,
  DocumentStats,
} from "@/types/document";
import { FileText, FileImage, Sheet, File, Paperclip } from "lucide-react";

/**
 * Upload a document (general or employee-specific)
 */
export async function uploadDocument(
  data: DocumentUploadRequest
): Promise<DocumentUploadResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Create FormData for file upload
  const formData = new FormData();
  formData.append("document_name", data.document_name);
  formData.append("document_category", data.document_category);
  formData.append("file", data.file);

  if (data.document_description) {
    formData.append("document_description", data.document_description);
  }

  if (data.expiry_date) {
    formData.append("expiry_date", data.expiry_date);
  }

  if (data.employee_id) {
    formData.append("employee_id", data.employee_id);
  }

  const url = createApiUrl("api/documents/upload");
  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      // Don't set Content-Type for FormData - browser will set it with boundary
    },
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to upload document");
  }

  return response.json();
}

/**
 * Get list of documents with optional filters
 */
export async function getDocuments(
  filters?: DocumentFilters
): Promise<DocumentListResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  let endpoint = "api/documents";
  const params = new URLSearchParams();

  if (filters) {
    if (filters.category) params.append("category", filters.category);
    if (filters.employee_id) params.append("employee_id", filters.employee_id);
    if (filters.search) params.append("search", filters.search);
    if (filters.expiry_status && filters.expiry_status !== "all") {
      params.append("expiry_status", filters.expiry_status);
    }
    if (filters.date_from) params.append("date_from", filters.date_from);
    if (filters.date_to) params.append("date_to", filters.date_to);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
  }

  if (params.toString()) {
    endpoint += `?${params.toString()}`;
  }

  return await apiGet<DocumentListResponse>(endpoint, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Get document by ID
 */
export async function getDocumentById(
  documentId: string
): Promise<DocumentDetailsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return await apiGet<DocumentDetailsResponse>(`api/documents/${documentId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Get expiring documents
 */
export async function getExpiringDocuments(
  daysAhead: number = 30
): Promise<ExpiringDocumentsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return await apiGet<ExpiringDocumentsResponse>(
    `api/documents/expiring?days_ahead=${daysAhead}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
}

/**
 * Download document
 */
export async function downloadDocument(
  documentId: string,
  fileName?: string
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    // First, get the download URL from the API
    const response = await apiGet<{
      download_url: string;
      filename: string;
      success: boolean;
    }>(`api/documents/${documentId}/download`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.success && response.download_url) {
      // Fetch the actual file from the download URL
      const fileResponse = await fetch(response.download_url);

      if (!fileResponse.ok) {
        throw new Error("Failed to download file");
      }

      // Get the file blob
      const blob = await fileResponse.blob();

      // Create a temporary link to download the file
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = fileName || response.filename || `document-${documentId}`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);
    } else {
      throw new Error("Failed to get download URL");
    }
  } catch (error) {
    console.error("Error downloading document:", error);
    throw error;
  }
}

/**
 * Delete document
 */
export async function deleteDocument(
  documentId: string
): Promise<{ success: boolean; message: string }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const url = createApiUrl(`api/documents/${documentId}`);
  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to delete document");
  }

  return response.json();
}

/**
 * Get Storage usage, the api return this response body:
 * {
    "company_id": "3aac1c1b-c598-401d-b9d6-0313c7c9e8bc",
    "success": true,
    "usage": {
        "object_count": 2,
        "total_bytes": 552153,
        "total_mb": 0.53
    }
}
 */
export async function getStorageUsage(): Promise<{
  company_id: string;
  success: boolean;
  usage: {
    object_count: number;
    total_bytes: number;
    total_mb: number;
  };
}> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }
  const url = createApiUrl("api/documents/storage/usage");
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to get storage usage");
  }

  return response.json();
}

/**
 * Validate file before upload
 */
export function validateFile(
  file: File,
  maxSize: number = 10 * 1024 * 1024
): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size must be less than ${Math.round(
        maxSize / (1024 * 1024)
      )}MB`,
    };
  }

  // Check file type
  const allowedTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "image/jpeg",
    "image/png",
    "image/gif",
    "text/plain",
  ];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error:
        "File type not supported. Please upload PDF, Word, Excel, Image, or Text files.",
    };
  }

  return { valid: true };
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Get file icon based on file type
 */
export function getFileIcon(fileType: string) {
  if (fileType.includes("pdf")) return FileText;
  if (fileType.includes("word")) return FileText;
  if (fileType.includes("excel") || fileType.includes("sheet")) return Sheet;
  if (fileType.includes("image")) return FileImage;
  if (fileType.includes("text")) return File;
  return Paperclip;
}
