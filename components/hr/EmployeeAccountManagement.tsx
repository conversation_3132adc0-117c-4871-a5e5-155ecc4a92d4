'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import CreateSingleAccount from './CreateSingleAccount';
import CreateBulkAccounts from './CreateBulkAccounts';
import CreateAllAccounts from './CreateAllAccounts';
import AccountCreationResults from './AccountCreationResults';

// Types for employee data
interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  employee_type_id: string | null;
  created_at?: string;
  updated_at?: string;
}

// Types for account creation responses
interface CreatedUser {
  department: string | null;
  employee_id: string;
  employee_name: string;
  generated_password: string;
  login_username: string;
  role: string;
  username: string;
}

interface SkippedUser {
  employee_id: string;
  employee_name: string;
  existing_username: string;
  reason: string;
}

interface FailedUser {
  employee_id: string;
  employee_name: string;
  error: string;
  reason: string;
}

interface AccountCreationResult {
  created_users: CreatedUser[];
  skipped_users: SkippedUser[];
  failed_users: FailedUser[];
  success: boolean;
  summary: {
    created: number;
    failed: number;
    skipped: number;
    total_requested?: number;
    total_employees?: number;
  };
}

interface SingleAccountResult {
  employee_info: {
    department: string;
    employee_id: string;
    name: string;
  };
  generated_password: string;
  login_username: string;
  message: string;
  success: boolean;
  user: {
    created_at: string;
    email: string;
    employee: {
      department_id: string;
      first_name: string;
      full_name: string;
      id: string;
      last_name: string;
    };
    employee_id: string;
    first_name: string;
    full_name: string;
    id: string;
    is_active: boolean;
    last_name: string;
    phone_number: string | null;
    role: string;
    updated_at: string | null;
    username: string;
  };
}

const EmployeeAccountManagement: React.FC = () => {
  const { companies } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);
  const [activeTab, setActiveTab] = useState<'single' | 'bulk' | 'all'>('single');
  const [lastResult, setLastResult] = useState<AccountCreationResult | SingleAccountResult | null>(null);
  const [error, setError] = useState('');

  // Fetch employees list with pagination to get all employees
  const fetchEmployees = useCallback(async () => {
    try {
      if (!companies || companies.length === 0) {
        console.log('No companies available');
        return;
      }

      setIsLoadingEmployees(true);
      setError('');

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        console.log('No access token available');
        setError('Authentication required');
        return;
      }

      console.log(`Fetching employees for company: ${companyId}`);

      let allEmployees: Employee[] = [];
      let currentPage = 1;
      let hasNextPage = true;

      // Fetch all pages of employees
      while (hasNextPage) {
        const response = await apiGet<{
          extend: {
            employees: Employee[];
            pagination?: {
              has_next: boolean;
              page: number;
              pages: number;
              per_page: number;
              total_count: number;
            };
          };
          msg: string;
        }>(
          `api/employees?company_id=${companyId}&page=${currentPage}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        if (response.extend && response.extend.employees && Array.isArray(response.extend.employees)) {
          allEmployees = [...allEmployees, ...response.extend.employees];

          // Check if there are more pages
          if (response.extend.pagination) {
            hasNextPage = response.extend.pagination.has_next;
            currentPage++;
          } else {
            // If no pagination info, assume this is the last page
            hasNextPage = false;
          }
        } else {
          hasNextPage = false;
        }
      }

      console.log('Found employees:', allEmployees.length);
      setEmployees(allEmployees);
    } catch (error: any) {
      console.error('Error fetching employees:', error);
      setError(`Failed to fetch employees: ${error.message}`);
    } finally {
      setIsLoadingEmployees(false);
    }
  }, [companies]);

  // Load employees on component mount
  useEffect(() => {
    fetchEmployees();
  }, [fetchEmployees]);

  // Handle account creation result
  const handleAccountCreationResult = (result: AccountCreationResult | SingleAccountResult) => {
    setLastResult(result);
    // Refresh employees list to get updated data
    fetchEmployees();
  };

  // Clear results
  const clearResults = () => {
    setLastResult(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Employee Account Management
          </h1>
          <p className="text-gray-600 mt-1">
            Create login accounts for employees to access their portal
          </p>
        </div>
        <div>
          <Link
            href="/dashboard/hr/employees"
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Employees
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Tab Navigation */}
      <DashboardCard title="Account Creation Options">
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('single')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'single'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Single Employee
            </button>
            <button
              onClick={() => setActiveTab('bulk')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'bulk'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Multiple Employees
            </button>
            <button
              onClick={() => setActiveTab('all')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'all'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              All Employees
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'single' && (
            <CreateSingleAccount
              employees={employees}
              isLoadingEmployees={isLoadingEmployees}
              onResult={handleAccountCreationResult}
            />
          )}

          {activeTab === 'bulk' && (
            <CreateBulkAccounts
              employees={employees}
              isLoadingEmployees={isLoadingEmployees}
              onResult={handleAccountCreationResult}
            />
          )}

          {activeTab === 'all' && (
            <CreateAllAccounts
              employees={employees}
              isLoadingEmployees={isLoadingEmployees}
              onResult={handleAccountCreationResult}
            />
          )}
        </div>
      </DashboardCard>

      {/* Results Display */}
      {lastResult && (
        <AccountCreationResults
          result={lastResult}
          onClear={clearResults}
        />
      )}
    </div>
  );
};

export default EmployeeAccountManagement;
