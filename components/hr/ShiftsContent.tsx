'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';
import ShiftModal from '@/components/shift/ShiftModal';
import ShiftCard from '@/components/shift/ShiftCard';
import EmployeeShiftAssignments from '@/components/shifts/EmployeeShiftAssignments';
import BulkEmployeeShiftAssignmentModal from '@/components/shifts/BulkEmployeeShiftAssignmentModal';
import { Shift, ShiftResponse, ShiftDeleteResponse } from '@/types/shift';
import {
  Users,
  Plus,
  Sparkles
} from 'lucide-react';

const ShiftsContent: React.FC = () => {
  const { companies } = useAuth();
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedShift, setSelectedShift] = useState<Shift | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [shiftToDelete, setShiftToDelete] = useState<Shift | null>(null);
  const [isBulkAssignModalOpen, setIsBulkAssignModalOpen] = useState(false);
  const [assignmentsRefreshTrigger, setAssignmentsRefreshTrigger] = useState(0);

  // Open create modal
  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  // Open edit modal
  const openEditModal = (shift: Shift) => {
    setSelectedShift(shift);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedShift(null);
    setIsEditModalOpen(false);
  };

  // Open delete confirmation
  const openDeleteConfirm = (shift: Shift) => {
    setShiftToDelete(shift);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setShiftToDelete(null);
    setIsDeleteConfirmOpen(false);
  };

  // Open bulk assignment modal
  const openBulkAssignModal = () => setIsBulkAssignModalOpen(true);

  // Close bulk assignment modal
  const closeBulkAssignModal = () => setIsBulkAssignModalOpen(false);

  // Fetch shifts when component mounts or companies change
  useEffect(() => {
    fetchShifts();
  }, [companies]);

  // Function to fetch shifts from API
  const fetchShifts = async () => {
    try {
      setIsLoading(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      const response = await apiGet<ShiftResponse>(`api/shifts?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('API response:', response);

      // Check for the nested structure with extend.shifts
      if (response.extend && response.extend.shifts) {
        setShifts(response.extend.shifts);
      } else if (response.shifts) {
        // Fallback to the original expected structure
        setShifts(response.shifts);
      } else {
        // If API call succeeds but doesn't return the expected data
        console.warn('API returned unexpected data format');
        setShifts([]);
      }
    } catch (error: any) {
      console.error('Error fetching shifts:', error);

      // Import the utility to check if error should be displayed
      const { shouldDisplayError } = await import('@/lib/auth-utils');

      // Only show error if it's not a session expiration or expected empty state
      if (shouldDisplayError(error)) {
        setError(error.message || 'Failed to fetch shifts');
      }

      setShifts([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to delete a shift
  const handleDeleteShift = async () => {
    if (!shiftToDelete) return;

    try {
      setIsLoading(true);

      const { apiDelete } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      const response = await apiDelete<ShiftDeleteResponse>(`api/shifts/${shiftToDelete.shift_id}?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Delete shift response:', response);

      // Check if the response indicates success
      if (response.code === 100 || response.success) {
        // Remove the deleted shift from the state
        setShifts(shifts.filter(shift => shift.shift_id !== shiftToDelete.shift_id));

        // Close the confirmation dialog
        closeDeleteConfirm();
      } else {
        throw new Error(response.msg || 'Failed to delete shift');
      }
    } catch (error: any) {
      console.error('Error deleting shift:', error);
      setError(error.message || 'Failed to delete shift');
    } finally {
      setIsLoading(false);
    }
  };

  // Group shifts by type (night shift, flexible, regular)
  const nightShifts = shifts.filter(shift => shift.is_night_shift);
  const flexibleShifts = shifts.filter(shift => shift.is_flexible && !shift.is_night_shift);
  const regularShifts = shifts.filter(shift => !shift.is_night_shift && !shift.is_flexible);

  return (
    <div className="space-y-6">
      {/* Create Shift Modal */}
      <ShiftModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSuccess={() => {
          closeCreateModal();
          fetchShifts();
        }}
      />

      {/* Edit Shift Modal */}
      <ShiftModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          fetchShifts();
        }}
        shift={selectedShift}
        isEditing={true}
      />

      {/* Bulk Assignment Modal */}
      <BulkEmployeeShiftAssignmentModal
        isOpen={isBulkAssignModalOpen}
        onClose={closeBulkAssignModal}
        onSuccess={() => {
          closeBulkAssignModal();
          // Refresh the assignments list
          setAssignmentsRefreshTrigger(prev => prev + 1);
        }}
      />

      {/* Delete Confirmation Dialog */}
      {isDeleteConfirmOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={closeDeleteConfirm}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            {/* Modal panel */}
            <div
              className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
              onClick={e => e.stopPropagation()}
            >
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Shift</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete the shift "{shiftToDelete?.name}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeleteShift}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeleteConfirm}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Shifts Management</h1>
        <div className="flex gap-3">
          <Link
            href="/dashboard/hr/shifts/advanced"
            className="btn-primary py-3 px-6 text-sm font-medium rounded-lg transition-all hover:bg-primary-dark flex items-center"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Advanced Scheduling
          </Link>
          <button
            className="bg-white text-gray-700 py-3 px-6 text-sm font-medium rounded-lg transition-all hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 flex items-center border border-gray-300"
            onClick={openBulkAssignModal}
          >
            <Users className="h-4 w-4 mr-2" />
            Bulk Assign
          </button>
          <button
            className="btn-primary py-3 px-6 text-sm font-medium rounded-lg transition-all hover:bg-primary-dark flex items-center"
            onClick={openCreateModal}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Shift
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Shifts</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="py-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-secondary">Loading shifts...</p>
        </div>
      ) : shifts.length === 0 ? (
        <DashboardCard title="Shifts Management">
          <div className="py-8 text-center">
            <p className="text-secondary">No shifts found.</p>
            <button
              onClick={openCreateModal}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Create your first shift
            </button>
          </div>
        </DashboardCard>
      ) : (
        <div className="space-y-6">
          {/* Night Shifts */}
          {nightShifts.length > 0 && (
            <DashboardCard title="Night Shifts">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
                {nightShifts.map(shift => (
                  <ShiftCard
                    key={shift.shift_id}
                    shift={shift}
                    onEdit={openEditModal}
                    onDelete={openDeleteConfirm}
                  />
                ))}
              </div>
            </DashboardCard>
          )}

          {/* Flexible Shifts */}
          {flexibleShifts.length > 0 && (
            <DashboardCard title="Flexible Shifts">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
                {flexibleShifts.map(shift => (
                  <ShiftCard
                    key={shift.shift_id}
                    shift={shift}
                    onEdit={openEditModal}
                    onDelete={openDeleteConfirm}
                  />
                ))}
              </div>
            </DashboardCard>
          )}

          {/* Regular Shifts */}
          {regularShifts.length > 0 && (
            <DashboardCard title="Regular Shifts">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
                {regularShifts.map(shift => (
                  <ShiftCard
                    key={shift.shift_id}
                    shift={shift}
                    onEdit={openEditModal}
                    onDelete={openDeleteConfirm}
                  />
                ))}
              </div>
            </DashboardCard>
          )}

          {/* Employee Shift Assignments */}
          <EmployeeShiftAssignments refreshTrigger={assignmentsRefreshTrigger} />
        </div>
      )}
    </div>
  );
};

export default ShiftsContent;
