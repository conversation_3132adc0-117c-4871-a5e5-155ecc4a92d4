import React from 'react';
import Link from 'next/link';
import { Construction, Info, ArrowLeft } from 'lucide-react';

interface ComingSoonProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  backLink?: string;
  backLinkText?: string;
}

const ComingSoon: React.FC<ComingSoonProps> = ({
  title,
  description,
  icon = <Construction className="h-16 w-16 text-orange-500" />,
  backLink = '/dashboard',
  backLinkText = 'Back to Dashboard'
}) => {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-4">
        <div className="mb-8">
          <div className="mb-4 flex justify-center">{icon}</div>
          <h1 className="text-3xl font-bold text-secondary-dark mb-4">{title}</h1>
          <p className="text-lg text-secondary mb-8">{description}</p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Info className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <h3 className="text-lg font-semibold text-blue-900 mb-2">We're Working on It!</h3>
          <p className="text-blue-700 text-sm">
            This feature is currently under development. We're working hard to bring you the best experience possible.
          </p>
        </div>

        <div className="space-y-4">
          <Link
            href={backLink}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {backLinkText}
          </Link>
          
          <div className="text-sm text-gray-500">
            <p>Need help? <Link href="/contact" className="text-primary hover:text-primary-dark">Contact our support team</Link></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComingSoon;
