"use client";

import React, { useState, useEffect } from "react";
import { SubscriptionPlan } from "@/types/subscription";
import {
  getSubscriptionPlans,
  formatFeaturesForDisplay,
  formatPrice,
  getPlanColor,
} from "@/lib/subscription";
import { Circle } from "lucide-react";

const FeatureComparison = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const fetchedPlans = await getSubscriptionPlans();
        // Sort plans by sort_order and filter active plans
        const sortedPlans = fetchedPlans
          .filter((plan) => plan.is_active)
          .sort((a, b) => a.sort_order - b.sort_order);
        setPlans(sortedPlans);
      } catch (err) {
        setError("Failed to load subscription plans. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, []);

  // Extract all unique features from all plans
  const getAllFeatures = () => {
    const featureMap = new Map<
      string,
      { name: string; values: { [planId: string]: boolean | string | number } }
    >();

    plans.forEach((plan) => {
      const features = formatFeaturesForDisplay(plan.features);
      features.forEach((feature) => {
        if (!featureMap.has(feature.name)) {
          featureMap.set(feature.name, { name: feature.name, values: {} });
        }

        // Determine feature value based on enabled status and limits
        let value: boolean | string | number = feature.enabled;

        if (
          feature.enabled &&
          feature.limit !== undefined &&
          feature.limit !== null
        ) {
          value = feature.limit === -1 ? "Unlimited" : feature.limit;
        } else if (feature.enabled && feature.description) {
          // Use description for complex features
          value = feature.description;
        }

        featureMap.get(feature.name)!.values[plan.plan_id] = value;
      });
    });

    return Array.from(featureMap.values());
  };

  if (isLoading) {
    return (
      <section className="section bg-background">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
                Feature Comparison by Plan
              </h2>
              <p className="text-secondary text-lg">
                Each KaziSync plan is tailored to meet the needs of businesses
                at different stages of growth.
              </p>
            </div>
            <div className="text-center py-16">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="mt-2 text-secondary">
                Loading feature comparison...
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error || plans.length === 0) {
    return (
      <section className="section bg-background">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
                Feature Comparison by Plan
              </h2>
              <p className="text-secondary text-lg">
                Each KaziSync plan is tailored to meet the needs of businesses
                at different stages of growth.
              </p>
            </div>
            <div className="text-center py-16">
              <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg max-w-md mx-auto">
                <p className="font-medium">Unable to load feature comparison</p>
                <p className="text-sm mt-1">
                  {error || "No subscription plans available"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  const allFeatures = getAllFeatures();

  // Helper function to get plan styling
  const getPlanStyling = (planName: string, sortOrder: number) => {
    return {
      text: "text-[#007BFF]",
      bg: "bg-[#007BFF]/10",
      border: "border-[#007BFF]/30",
      icon: <Circle className="h-4 w-4 fill-[#007BFF] text-[#007BFF]" />,
    };
  };

  // Helper function to format plan pricing
  const formatPlanPrice = (
    flatPrice: number,
    pricePerEmployee: number,
    billingCycle: string
  ) => {
    const formattedFlat = formatPrice(flatPrice);
    const formattedPerEmployee = formatPrice(pricePerEmployee);
    const cycle =
      billingCycle === "monthly"
        ? "/mo"
        : billingCycle === "yearly"
        ? "/yr"
        : "";
    return `${formattedFlat}${cycle} + ${formattedPerEmployee}/user`;
  };
  const renderFeatureValue = (value: boolean | string | number | undefined) => {
    if (value === true) {
      return (
        <div className="flex justify-center">
          <svg
            className="h-5 w-5 text-success"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
      );
    } else if (value === false || value === undefined) {
      return (
        <div className="flex justify-center">
          <svg
            className="h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>
      );
    } else {
      return (
        <div className="text-center text-sm text-secondary-dark font-medium">
          {value}
        </div>
      );
    }
  };

  return (
    <section className="section bg-background">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
              Feature Comparison by Plan
            </h2>
            <p className="text-secondary text-lg">
              Each KaziSync plan is tailored to meet the needs of businesses at
              different stages of growth.
            </p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-secondary-dark">
                      Feature
                    </th>
                    {plans.map((plan, index) => {
                      const planStyling = getPlanStyling(
                        plan.name,
                        plan.sort_order
                      );
                      return (
                        <th
                          key={plan.plan_id}
                          className={`px-6 py-4 text-center text-sm font-semibold ${planStyling.text}`}
                        >
                          <div className="flex items-center justify-center gap-2">
                            {planStyling.icon} {plan.name}
                          </div>
                          <span className="font-normal text-xs">
                            {formatPlanPrice(
                              plan.flat_price,
                              plan.price_per_employee,
                              plan.billing_cycle
                            )}
                          </span>
                        </th>
                      );
                    })}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {allFeatures.map((feature, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm text-secondary-dark font-medium">
                        {feature.name}
                      </td>
                      {plans.map((plan) => (
                        <td key={plan.plan_id} className="px-6 py-4">
                          {renderFeatureValue(feature.values[plan.plan_id])}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* <div
            className={`mt-12 grid grid-cols-1 ${
              plans.length > 2
                ? "md:grid-cols-3"
                : plans.length === 2
                ? "md:grid-cols-2"
                : "md:grid-cols-1"
            } gap-6`}
          >
            {plans.map((plan, index) => {
              const planStyling = getPlanStyling(plan.name, plan.sort_order);
              return (
                <div
                  key={plan.plan_id}
                  className={`p-6 ${planStyling.bg} rounded-lg border ${planStyling.border}`}
                >
                  <h3
                    className={`text-lg font-semibold ${planStyling.text.replace(
                      "700",
                      "800"
                    )} mb-2 flex items-center gap-2`}
                  >
                    {planStyling.icon} {plan.name} –{" "}
                    {formatPlanPrice(
                      plan.flat_price,
                      plan.price_per_employee,
                      plan.billing_cycle
                    )}
                  </h3>
                  <p className={`${planStyling.text} text-sm`}>
                    {plan.description}
                  </p>
                </div>
              );
            })}
          </div> */}
        </div>
      </div>
    </section>
  );
};

export default FeatureComparison;
