"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardCard from "@/components/ui/DashboardCard";
import EmployeeShiftAssignmentModal from "./EmployeeShiftAssignmentModal";
import AssignmentDetailsModal from "./AssignmentDetailsModal";
import { Shift as ShiftType } from "@/types/shift";

// Define interfaces
type Shift = ShiftType;

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

interface ShiftAssignment {
  assignment_id: string;
  employee_id: string;
  shift_id: string;
  effective_start_date: string;
  effective_end_date: string;
  custom_start_time: string;
  custom_end_time: string;
  custom_break_duration: number;
  custom_working_days: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  employee?: Employee;
  shift?: Shift;
}

interface EmployeeShiftAssignmentsProps {
  shiftId?: string;
  employeeId?: string;
  refreshTrigger?: number;
}

const EmployeeShiftAssignments: React.FC<EmployeeShiftAssignmentsProps> = ({
  shiftId,
  employeeId,
  refreshTrigger = 0,
}) => {
  const { companies } = useAuth();
  const [assignments, setAssignments] = useState<ShiftAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);

  // Modal states
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedShift, setSelectedShift] = useState<Shift | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [selectedAssignment, setSelectedAssignment] =
    useState<ShiftAssignment | null>(null);

  // Fetch data on component mount and when refreshTrigger changes
  useEffect(() => {
    fetchAssignments();
    fetchShifts();
    fetchEmployees();
  }, [companies, shiftId, employeeId, refreshTrigger]); // eslint-disable-line react-hooks/exhaustive-deps

  // Fetch shift assignments with pagination
  const fetchAssignments = async () => {
    try {
      setIsLoading(true);
      setError("");

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import("@/lib/api");
      const { getAccessToken } = await import("@/lib/auth");
      const token = getAccessToken();

      if (!token) {
        throw new Error("Authentication required");
      }

      let allAssignments: ShiftAssignment[] = [];
      let currentPage = 1;
      let hasNextPage = true;

      // Fetch all pages of assignments
      while (hasNextPage) {
        let endpoint = `api/employee-shifts?company_id=${companyId}&page=${currentPage}`;

        // Add filter parameters if provided
        if (shiftId) {
          endpoint += `&shift_id=${shiftId}`;
        }

        if (employeeId) {
          endpoint += `&employee_id=${employeeId}`;
        }

        const response = await apiGet<{
          extend: {
            assignments: ShiftAssignment[];
            pagination?: {
              has_next: boolean;
              page: number;
              pages: number;
              per_page: number;
              total_count: number;
            };
          };
          msg: string;
        }>(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.extend && response.extend.assignments) {
          allAssignments = [...allAssignments, ...response.extend.assignments];

          // Check if there are more pages
          if (response.extend.pagination) {
            hasNextPage = response.extend.pagination.has_next;
            currentPage++;
          } else {
            // If no pagination info, assume this is the last page
            hasNextPage = false;
          }
        } else {
          hasNextPage = false;
        }
      }

      setAssignments(allAssignments);
    } catch (error: any) {
      setError(error.message || "Failed to fetch shift assignments");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch shifts
  const fetchShifts = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import("@/lib/api");
      const { getAccessToken } = await import("@/lib/auth");
      const token = getAccessToken();

      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await apiGet<{ extend: { shifts: Shift[] } }>(
        `api/shifts?company_id=${companyId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.extend && response.extend.shifts) {
        setShifts(response.extend.shifts);

        // If shiftId is provided, find the shift
        if (shiftId) {
          const shift = response.extend.shifts.find(
            (s) => s.shift_id === shiftId
          );
          if (shift) {
            setSelectedShift(shift);
          }
        }
      }
    } catch (error: any) {
      console.error("Failed to fetch shifts:", error);
    }
  };

  // Fetch employees with pagination to get all employees
  const fetchEmployees = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import("@/lib/api");
      const { getAccessToken } = await import("@/lib/auth");
      const token = getAccessToken();

      if (!token) {
        throw new Error("Authentication required");
      }

      let allEmployees: Employee[] = [];
      let currentPage = 1;
      let hasNextPage = true;

      // Fetch all pages of employees
      while (hasNextPage) {
        const response = await apiGet<{
          extend: {
            employees: Employee[];
            pagination?: {
              has_next: boolean;
              page: number;
              pages: number;
              per_page: number;
              total_count: number;
            };
          };
          msg: string;
        }>(`api/employees?company_id=${companyId}&page=${currentPage}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.extend && response.extend.employees) {
          allEmployees = [...allEmployees, ...response.extend.employees];

          // Check if there are more pages
          if (response.extend.pagination) {
            hasNextPage = response.extend.pagination.has_next;
            currentPage++;
          } else {
            // If no pagination info, assume this is the last page
            hasNextPage = false;
          }
        } else {
          hasNextPage = false;
        }
      }

      setEmployees(allEmployees);

      // If employeeId is provided, find the employee
      if (employeeId) {
        const employee = allEmployees.find((e) => e.employee_id === employeeId);
        if (employee) {
          setSelectedEmployee(employee);
        }
      }
    } catch (error: any) {
      console.error("Failed to fetch employees:", error);
    }
  };

  // Get shift name by ID
  const getShiftName = (shiftId: string) => {
    const shift = shifts.find((s) => s.shift_id === shiftId);
    return shift ? shift.name : "Unknown Shift";
  };

  // Get employee name by ID
  const getEmployeeName = (employeeId: string) => {
    const employee = employees.find((e) => e.employee_id === employeeId);
    return employee ? employee.full_name : "Unknown Employee";
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Format working days for display
  const formatWorkingDays = (daysString: string | null | undefined) => {
    // Handle null, undefined, or empty string
    if (!daysString || daysString.trim() === "") {
      return "No working days set";
    }

    const dayMap: { [key: string]: string } = {
      "0": "Sun",
      "1": "Mon",
      "2": "Tue",
      "3": "Wed",
      "4": "Thu",
      "5": "Fri",
      "6": "Sat",
    };

    try {
      return daysString
        .split(",")
        .map((day) => day.trim()) // Trim whitespace
        .filter((day) => day !== "") // Remove empty strings
        .map((day) => dayMap[day] || day)
        .join(", ");
    } catch (error) {
      console.warn("Error formatting working days:", error);
      return "Invalid working days format";
    }
  };

  // Open assignment modal
  const openAssignModal = () => {
    setIsAssignModalOpen(true);
  };

  // Close assignment modal
  const closeAssignModal = () => {
    setIsAssignModalOpen(false);
  };

  // Open details modal
  const openDetailsModal = (assignment: ShiftAssignment) => {
    // Enrich the assignment with employee and shift data if not already present
    const enrichedAssignment = { ...assignment };

    if (!enrichedAssignment.employee && enrichedAssignment.employee_id) {
      const employee = employees.find(
        (e) => e.employee_id === enrichedAssignment.employee_id
      );
      if (employee) {
        enrichedAssignment.employee = employee;
      }
    }

    if (!enrichedAssignment.shift && enrichedAssignment.shift_id) {
      const shift = shifts.find(
        (s) => s.shift_id === enrichedAssignment.shift_id
      );
      if (shift) {
        enrichedAssignment.shift = shift;
      }
    }

    setSelectedAssignment(enrichedAssignment);
    setIsDetailsModalOpen(true);
  };

  // Close details modal
  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedAssignment(null);
  };

  // Handle successful assignment
  const handleAssignmentSuccess = () => {
    fetchAssignments();
  };

  return (
    <>
      <EmployeeShiftAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={closeAssignModal}
        onSuccess={handleAssignmentSuccess}
        shift={selectedShift}
        employee={selectedEmployee}
      />

      {selectedAssignment && (
        <AssignmentDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={closeDetailsModal}
          assignment={selectedAssignment}
        />
      )}

      <DashboardCard
        title={
          shiftId
            ? `Employees in ${getShiftName(shiftId)}`
            : employeeId
            ? `Shifts for ${getEmployeeName(employeeId)}`
            : "Employee Shift Assignments"
        }
      >
        <div className="mb-4 flex justify-between items-center">
          <p className="text-sm text-secondary">
            {shiftId
              ? "Employees assigned to this shift"
              : employeeId
              ? "Shifts assigned to this employee"
              : "All shift assignments"}
          </p>
          <button
            className="btn-primary py-3 px-6 text-sm font-medium rounded-lg transition-all hover:bg-primary-dark flex items-center"
            onClick={openAssignModal}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Assign Employee to Shift
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading shift assignments...</p>
          </div>
        ) : assignments.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-secondary">No shift assignments found.</p>
            <button
              onClick={openAssignModal}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Assign an employee to a shift
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Employee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Shift
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Effective Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Custom Times
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Working Days
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {assignments.map((assignment) => (
                  <tr
                    key={assignment.assignment_id}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-secondary-dark">
                        {assignment.employee
                          ? assignment.employee.full_name
                          : getEmployeeName(assignment.employee_id)}
                      </div>
                      {assignment.employee && (
                        <div className="text-xs text-secondary">
                          {assignment.employee.position || "No position"}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-secondary-dark">
                        {assignment.shift
                          ? assignment.shift.name
                          : getShiftName(assignment.shift_id)}
                      </div>
                      {assignment.shift && (
                        <div className="text-xs text-secondary">
                          {assignment.shift.start_time} -{" "}
                          {assignment.shift.end_time}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-secondary-dark">
                        {formatDate(assignment.effective_start_date)} -{" "}
                        {formatDate(assignment.effective_end_date)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-secondary-dark">
                        {assignment.custom_start_time} -{" "}
                        {assignment.custom_end_time}
                      </div>
                      <div className="text-xs text-secondary">
                        Break: {assignment.custom_break_duration} min
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-secondary-dark">
                        {formatWorkingDays(assignment.custom_working_days)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          assignment.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {assignment.is_active ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-primary hover:text-primary-dark mr-3"
                        onClick={() => openDetailsModal(assignment)}
                      >
                        View
                      </button>
                      <button
                        className="text-secondary-dark hover:text-secondary"
                        onClick={() => {
                          /* Edit assignment */
                        }}
                      >
                        Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </DashboardCard>
    </>
  );
};

export default EmployeeShiftAssignments;
