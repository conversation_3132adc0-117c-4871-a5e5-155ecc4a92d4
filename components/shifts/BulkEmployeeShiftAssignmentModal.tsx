'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Shift as ShiftType } from '@/types/shift';

// Define interfaces
type Shift = ShiftType;

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

interface BulkAssignmentResult {
  assignments: Array<{
    assignment_id: string;
    employee_id: string;
    shift_id: string;
    effective_start_date: string;
    effective_end_date: string;
    employee: Employee;
    shift: Shift;
  }>;
  message: string;
  summary: {
    failed: number;
    successful: number;
    total_requested: number;
    validation_errors: number;
  };
  validation_results: Array<{
    employee_id: string;
    message: string;
    status: 'success' | 'error';
    assignment_id?: string;
  }>;
}

interface BulkEmployeeShiftAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  shift?: Shift | null;
}

const BulkEmployeeShiftAssignmentModal: React.FC<BulkEmployeeShiftAssignmentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  shift
}) => {
  const { companies } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [assignmentResult, setAssignmentResult] = useState<BulkAssignmentResult | null>(null);

  // Form state
  const [selectedShift, setSelectedShift] = useState<string>(shift?.shift_id || '');
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [effectiveStartDate, setEffectiveStartDate] = useState<Date | null>(new Date());
  const [effectiveEndDate, setEffectiveEndDate] = useState<Date | null>(
    new Date(new Date().setMonth(new Date().getMonth() + 6))
  );

  // Lists for dropdowns
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoadingShifts, setIsLoadingShifts] = useState(false);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);

  // Initialize form with shift data if provided
  useEffect(() => {
    if (shift) {
      setSelectedShift(shift.shift_id);
    }
  }, [shift]);

  // Fetch shifts and employees when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchShifts();
      fetchEmployees();
      // Reset form state
      setSelectedEmployees([]);
      setSearchTerm('');
      setError('');
      setSuccess('');
      setAssignmentResult(null);
      setShowResults(false);
    }
  }, [isOpen]); // eslint-disable-line react-hooks/exhaustive-deps

  // Filter employees based on search term
  const filteredEmployees = employees.filter(employee =>
    employee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.position || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Fetch shifts
  const fetchShifts = async () => {
    try {
      setIsLoadingShifts(true);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {shifts: Shift[]}}>(
        `api/shifts?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.shifts) {
        setShifts(response.extend.shifts);

        // If no shift was provided but we have shifts, select the first one
        if (!shift && response.extend.shifts.length > 0) {
          setSelectedShift(response.extend.shifts[0].shift_id);
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch shifts');
    } finally {
      setIsLoadingShifts(false);
    }
  };

  // Fetch employees with pagination to get all employees
  const fetchEmployees = async () => {
    try {
      setIsLoadingEmployees(true);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      let allEmployees: Employee[] = [];
      let currentPage = 1;
      let hasNextPage = true;

      // Fetch all pages of employees
      while (hasNextPage) {
        const response = await apiGet<{
          extend: {
            employees: Employee[];
            pagination?: {
              has_next: boolean;
              page: number;
              pages: number;
              per_page: number;
              total_count: number;
            };
          };
          msg: string;
        }>(
          `api/employees?company_id=${companyId}&page=${currentPage}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        if (response.extend && response.extend.employees) {
          allEmployees = [...allEmployees, ...response.extend.employees];

          // Check if there are more pages
          if (response.extend.pagination) {
            hasNextPage = response.extend.pagination.has_next;
            currentPage++;
          } else {
            // If no pagination info, assume this is the last page
            hasNextPage = false;
          }
        } else {
          hasNextPage = false;
        }
      }

      // Filter to only active employees
      const activeEmployees = allEmployees.filter(emp => emp.status === 'active');
      setEmployees(activeEmployees);
    } catch (error: any) {
      setError(error.message || 'Failed to fetch employees');
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Handle employee selection toggle
  const handleEmployeeToggle = (employeeId: string) => {
    setSelectedEmployees(prev => {
      if (prev.includes(employeeId)) {
        return prev.filter(id => id !== employeeId);
      } else {
        return [...prev, employeeId];
      }
    });
  };

  // Select all filtered employees
  const handleSelectAll = () => {
    const filteredIds = filteredEmployees.map(emp => emp.employee_id);
    const allSelected = filteredIds.every(id => selectedEmployees.includes(id));
    
    if (allSelected) {
      // Deselect all filtered employees
      setSelectedEmployees(prev => prev.filter(id => !filteredIds.includes(id)));
    } else {
      // Select all filtered employees
      const combinedIds = [...selectedEmployees, ...filteredIds];
      const uniqueIds = Array.from(new Set(combinedIds));
      setSelectedEmployees(uniqueIds);
    }
  };

  // Clear all selections
  const handleClearAll = () => {
    setSelectedEmployees([]);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      setError('');
      setSuccess('');

      if (!companies || companies.length === 0) {
        throw new Error('No company found');
      }

      if (!selectedShift) {
        throw new Error('Please select a shift');
      }

      if (selectedEmployees.length === 0) {
        throw new Error('Please select at least one employee');
      }

      if (!effectiveStartDate) {
        throw new Error('Please select an effective start date');
      }

      if (!effectiveEndDate) {
        throw new Error('Please select an effective end date');
      }

      const companyId = companies[0].company_id;
      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const formattedStartDate = effectiveStartDate.toISOString().split('T')[0];
      const formattedEndDate = effectiveEndDate.toISOString().split('T')[0];

      const assignmentData = {
        company_id: companyId,
        shift_id: selectedShift,
        employee_ids: selectedEmployees,
        effective_start_date: formattedStartDate,
        effective_end_date: formattedEndDate
      };

      const response = await apiPost<BulkAssignmentResult>('api/employee-shifts/bulk', assignmentData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setAssignmentResult(response);
      setShowResults(true);
      setSuccess(`Bulk assignment completed. ${response.summary.successful} successful, ${response.summary.failed} failed.`);

    } catch (error: any) {
      setError(error.message || 'Failed to assign employees to shift');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-60 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:pt-0 sm:px-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Bulk Assign Employees to Shift
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="mt-2 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
                    {success}
                  </div>
                )}

                {!showResults ? (
                  <form className="mt-4">
                    <div className="space-y-6">
                      {/* Shift Selection */}
                      <div>
                        <label htmlFor="shift" className="block text-sm font-medium text-secondary-dark mb-1">
                          Shift
                        </label>
                        <select
                          id="shift"
                          value={selectedShift}
                          onChange={(e) => setSelectedShift(e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          disabled={!!shift || isLoadingShifts}
                        >
                          {isLoadingShifts ? (
                            <option>Loading shifts...</option>
                          ) : shifts.length === 0 ? (
                            <option>No shifts available</option>
                          ) : (
                            <>
                              <option value="">Select a shift</option>
                              {shifts.map((s) => (
                                <option key={s.shift_id} value={s.shift_id}>
                                  {s.name} ({s.start_time} - {s.end_time})
                                </option>
                              ))}
                            </>
                          )}
                        </select>
                      </div>

                      {/* Effective Date Range */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-secondary-dark mb-1">
                            Start Date
                          </label>
                          <DatePicker
                            selected={effectiveStartDate}
                            onChange={(date) => setEffectiveStartDate(date)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            dateFormat="yyyy-MM-dd"
                            minDate={new Date()}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-secondary-dark mb-1">
                            End Date
                          </label>
                          <DatePicker
                            selected={effectiveEndDate}
                            onChange={(date) => setEffectiveEndDate(date)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            dateFormat="yyyy-MM-dd"
                            minDate={effectiveStartDate || undefined}
                          />
                        </div>
                      </div>

                      {/* Employee Selection */}
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <label className="block text-sm font-medium text-secondary-dark">
                            Select Employees ({selectedEmployees.length} selected)
                          </label>
                          <div className="flex gap-2">
                            <button
                              type="button"
                              onClick={handleSelectAll}
                              className="text-sm text-primary hover:text-primary-dark"
                            >
                              {filteredEmployees.every(emp => selectedEmployees.includes(emp.employee_id)) ? 'Deselect All' : 'Select All'}
                            </button>
                            {selectedEmployees.length > 0 && (
                              <button
                                type="button"
                                onClick={handleClearAll}
                                className="text-sm text-red-600 hover:text-red-800"
                              >
                                Clear All
                              </button>
                            )}
                          </div>
                        </div>

                        {/* Search */}
                        <div className="mb-3">
                          <input
                            type="text"
                            placeholder="Search employees by name, email, or position..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>

                        {/* Employee List */}
                        <div className="border border-gray-300 rounded-md max-h-64 overflow-y-auto">
                          {isLoadingEmployees ? (
                            <div className="p-4 text-center text-gray-500">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                              Loading employees...
                            </div>
                          ) : filteredEmployees.length === 0 ? (
                            <div className="p-4 text-center text-gray-500">
                              {searchTerm ? `No employees found matching "${searchTerm}"` : 'No employees available'}
                            </div>
                          ) : (
                            <div className="divide-y divide-gray-200">
                              {filteredEmployees.map((employee) => (
                                <div
                                  key={employee.employee_id}
                                  className={`p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                                    selectedEmployees.includes(employee.employee_id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                                  }`}
                                  onClick={() => handleEmployeeToggle(employee.employee_id)}
                                >
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={selectedEmployees.includes(employee.employee_id)}
                                      onChange={() => handleEmployeeToggle(employee.employee_id)}
                                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    />
                                    <div className="ml-3 flex-1">
                                      <div className="flex items-center justify-between">
                                        <div>
                                          <h4 className="text-sm font-medium text-gray-900">{employee.full_name}</h4>
                                          <p className="text-sm text-gray-500">
                                            {employee.position || 'No Position'} • {employee.email || 'No Email'}
                                          </p>
                                        </div>
                                        <div className="flex-shrink-0">
                                          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                            <span className="text-sm font-medium text-primary">
                                              {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </form>
                ) : (
                  // Results view
                  <div className="mt-4">
                    {assignmentResult && (
                      <div className="space-y-4">
                        {/* Summary */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-lg font-medium text-gray-900 mb-2">Assignment Summary</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-green-600">{assignmentResult.summary.successful}</div>
                              <div className="text-gray-600">Successful</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-red-600">{assignmentResult.summary.failed}</div>
                              <div className="text-gray-600">Failed</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-yellow-600">{assignmentResult.summary.validation_errors}</div>
                              <div className="text-gray-600">Validation Errors</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-600">{assignmentResult.summary.total_requested}</div>
                              <div className="text-gray-600">Total Requested</div>
                            </div>
                          </div>
                        </div>

                        {/* Detailed Results */}
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-2">Detailed Results</h4>
                          <div className="border border-gray-300 rounded-md max-h-64 overflow-y-auto">
                            <div className="divide-y divide-gray-200">
                              {assignmentResult.validation_results.map((result, index) => {
                                const employee = employees.find(emp => emp.employee_id === result.employee_id);
                                return (
                                  <div
                                    key={index}
                                    className={`p-3 ${
                                      result.status === 'success' ? 'bg-green-50' : 'bg-red-50'
                                    }`}
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center">
                                        <div className={`w-3 h-3 rounded-full mr-3 ${
                                          result.status === 'success' ? 'bg-green-500' : 'bg-red-500'
                                        }`}></div>
                                        <div>
                                          <div className="font-medium text-gray-900">
                                            {employee?.full_name || 'Unknown Employee'}
                                          </div>
                                          <div className="text-sm text-gray-600">
                                            {employee?.position || 'No Position'} • {employee?.email || 'No Email'}
                                          </div>
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <div className={`text-sm font-medium ${
                                          result.status === 'success' ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                          {result.status === 'success' ? 'Success' : 'Failed'}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                          {result.message}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </div>

                        {/* Successful Assignments */}
                        {assignmentResult.assignments.length > 0 && (
                          <div>
                            <h4 className="text-lg font-medium text-gray-900 mb-2">
                              Successfully Assigned Employees ({assignmentResult.assignments.length})
                            </h4>
                            <div className="border border-gray-300 rounded-md max-h-48 overflow-y-auto">
                              <div className="divide-y divide-gray-200">
                                {assignmentResult.assignments.map((assignment, index) => (
                                  <div key={index} className="p-3 bg-green-50">
                                    <div className="flex items-center justify-between">
                                      <div>
                                        <div className="font-medium text-gray-900">
                                          {assignment.employee.full_name}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                          {assignment.employee.position || 'No Position'}
                                        </div>
                                      </div>
                                      <div className="text-right text-sm text-gray-600">
                                        <div>{assignment.effective_start_date} to {assignment.effective_end_date}</div>
                                        <div className="text-xs">ID: {assignment.assignment_id}</div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            {!showResults ? (
              <>
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handleSubmit}
                  disabled={isLoading || selectedEmployees.length === 0 || !selectedShift}
                >
                  {isLoading ? 'Assigning...' : `Assign ${selectedEmployees.length} Employee${selectedEmployees.length !== 1 ? 's' : ''}`}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </>
            ) : (
              <button
                type="button"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                onClick={() => {
                  onSuccess();
                  onClose();
                }}
              >
                Done
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkEmployeeShiftAssignmentModal;
