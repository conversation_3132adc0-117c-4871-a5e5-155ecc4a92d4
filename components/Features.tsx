import React from 'react';
import { Clock, Calendar, TrendingUp, Users, BarChart3 } from 'lucide-react';

const features = [
  {
    title: 'Real-Time Attendance Tracking',
    description: 'Seamlessly integrated with biometric and facial recognition devices.',
    icon: <Clock className="h-10 w-10 text-primary" />,
  },
  // {
  //   title: '💸 Payroll Management',
  //   description: 'Automate payroll calculations, tax deductions, benefits, and QuickBooks integration.',
  //   icon: (
  //     <svg
  //       className="h-10 w-10 text-primary"
  //       fill="none"
  //       viewBox="0 0 24 24"
  //       stroke="currentColor"
  //     >
  //       <path
  //         strokeLinecap="round"
  //         strokeLinejoin="round"
  //         strokeWidth={1.5}
  //         d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  //       />
  //     </svg>
  //   ),
  // },
  {
    title: 'Leave & Time-Off Management',
    description: 'Smart leave policies with request, approval, and calendar sync.',
    icon: <Calendar className="h-10 w-10 text-primary" />,
  },
  {
    title: 'Performance Reviews',
    description: 'Track goals, set KPIs, and run performance cycles with actionable insights.',
    icon: <TrendingUp className="h-10 w-10 text-primary" />,
  },
  {
    title: 'Employee Onboarding',
    description: 'Simplify hiring and onboarding with digital document collection and workflows.',
    icon: <Users className="h-10 w-10 text-primary" />,
  },
  // {
  //   title: '💰 Loan & Advance Management',
  //   description: 'Enable transparent and secure salary advance requests and loan tracking.',
  //   icon: (
  //     <svg
  //       className="h-10 w-10 text-primary"
  //       fill="none"
  //       viewBox="0 0 24 24"
  //       stroke="currentColor"
  //     >
  //       <path
  //         strokeLinecap="round"
  //         strokeLinejoin="round"
  //         strokeWidth={1.5}
  //         d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
  //       />
  //     </svg>
  //   ),
  // },
  // {
  //   title: '🗂️ Centralized Document Storage',
  //   description: 'Manage resumes, contracts, IDs, and HR documents per employee or department.',
  //   icon: (
  //     <svg
  //       className="h-10 w-10 text-primary"
  //       fill="none"
  //       viewBox="0 0 24 24"
  //       stroke="currentColor"
  //     >
  //       <path
  //         strokeLinecap="round"
  //         strokeLinejoin="round"
  //         strokeWidth={1.5}
  //         d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
  //       />
  //     </svg>
  //   ),
  // },
  {
    title: 'HR Analytics Dashboard',
    description: 'Visualize metrics that matter — from headcount to attrition to performance trends.',
    icon: <BarChart3 className="h-10 w-10 text-primary" />,
  },
];

const Features = () => {
  return (
    <section id="features" className="section bg-white">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            Core Features That Work in Sync
          </h2>
          <p className="text-secondary text-lg">
            KaziSync offers a comprehensive suite of features designed to streamline your HR operations from payroll to performance management.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="card p-6 group"
            >
              <div className="mb-4 transform group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-secondary-dark mb-2">{feature.title}</h3>
              <p className="text-secondary">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
