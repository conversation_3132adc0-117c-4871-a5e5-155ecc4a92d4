import React from 'react';
import Link from 'next/link';

const Hero = () => {
  return (
    <section className="bg-gradient-to-b from-background to-background-dark py-16 md:py-24">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-dark mb-6">
              KaziSync – <span className="text-primary">Work in Sync</span>
            </h1>
            <p className="text-lg text-secondary mb-8 max-w-lg">
              The modern HRMS built for teams of all sizes. From payroll to performance, KaziS<PERSON> helps you manage people, not paperwork.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/signup" className="btn-primary text-center px-8 py-3">
                Get Started
              </Link>
              {/* <Link href="#demo" className="btn-outline text-center px-8 py-3">
                Book Demo
              </Link> */}
              <Link href="#contact" className="btn-outline text-center px-8 py-3">
                Contact Sales
              </Link>
            </div>
            <div className="mt-8 flex items-center text-sm text-secondary">
              <svg
                className="h-5 w-5 mr-2 text-success"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span>Try the platform free for 14 days</span>
            </div>
          </div>
          <div className="relative animate-fade-in">
            <div className="bg-white rounded-2xl border border-gray-200 p-4 md:p-8 relative z-10">
              <div className="aspect-w-16 aspect-h-9 bg-gradient-to-br from-slate-50 to-slate-100 rounded-lg overflow-hidden p-6">
                {/* Professional Dashboard Preview */}
                <svg
                  className="w-full h-full"
                  viewBox="0 0 800 450"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  {/* Background */}
                  <rect width="800" height="450" fill="#F8FAFC"/>

                  {/* Header */}
                  <rect x="0" y="0" width="800" height="60" fill="white"/>
                  <rect x="0" y="60" width="800" height="1" fill="#E2E8F0"/>

                  {/* Logo Area */}
                  <rect x="20" y="15" width="120" height="30" rx="4" fill="#3B82F6"/>
                  <text x="80" y="35" textAnchor="middle" fill="white" fontSize="14" fontWeight="600">KaziSync</text>

                  {/* Navigation */}
                  <rect x="600" y="20" width="60" height="20" rx="10" fill="#EFF6FF"/>
                  <rect x="680" y="20" width="60" height="20" rx="10" fill="#3B82F6"/>
                  <text x="710" y="33" textAnchor="middle" fill="white" fontSize="12">Dashboard</text>

                  {/* Sidebar */}
                  <rect x="0" y="61" width="200" height="389" fill="white"/>
                  <rect x="200" y="61" width="1" height="389" fill="#E2E8F0"/>

                  {/* Sidebar Items */}
                  <rect x="15" y="80" width="170" height="35" rx="6" fill="#EFF6FF"/>
                  <circle cx="35" cy="97" r="8" fill="#3B82F6"/>
                  <rect x="50" y="92" width="80" height="10" rx="5" fill="#1E293B"/>

                  <rect x="15" y="125" width="170" height="35" rx="6" fill="#F8FAFC"/>
                  <circle cx="35" cy="142" r="8" fill="#64748B"/>
                  <rect x="50" y="137" width="70" height="10" rx="5" fill="#64748B"/>

                  <rect x="15" y="170" width="170" height="35" rx="6" fill="#F8FAFC"/>
                  <circle cx="35" cy="187" r="8" fill="#64748B"/>
                  <rect x="50" y="182" width="90" height="10" rx="5" fill="#64748B"/>

                  {/* Main Content Area */}
                  <rect x="201" y="61" width="599" height="389" fill="#F8FAFC"/>

                  {/* Page Title */}
                  <text x="230" y="100" fill="#1E293B" fontSize="24" fontWeight="700">HR Dashboard</text>
                  <text x="230" y="125" fill="#64748B" fontSize="14">Welcome back! Here's what's happening with your team today.</text>

                  {/* Stats Cards */}
                  <rect x="230" y="150" width="130" height="80" rx="8" fill="white"/>
                  <rect x="230" y="150" width="130" height="4" rx="2" fill="#10B981"/>
                  <text x="245" y="175" fill="#64748B" fontSize="12">Total Employees</text>
                  <text x="245" y="200" fill="#1E293B" fontSize="28" fontWeight="700">247</text>
                  <text x="245" y="220" fill="#10B981" fontSize="11">+12 this month</text>

                  <rect x="380" y="150" width="130" height="80" rx="8" fill="white"/>
                  <rect x="380" y="150" width="130" height="4" rx="2" fill="#3B82F6"/>
                  <text x="395" y="175" fill="#64748B" fontSize="12">Present Today</text>
                  <text x="395" y="200" fill="#1E293B" fontSize="28" fontWeight="700">234</text>
                  <text x="395" y="220" fill="#3B82F6" fontSize="11">94.7% attendance</text>

                  <rect x="530" y="150" width="130" height="80" rx="8" fill="white"/>
                  <rect x="530" y="150" width="130" height="4" rx="2" fill="#F59E0B"/>
                  <text x="545" y="175" fill="#64748B" fontSize="12">On Leave</text>
                  <text x="545" y="200" fill="#1E293B" fontSize="28" fontWeight="700">8</text>
                  <text x="545" y="220" fill="#F59E0B" fontSize="11">3 pending</text>

                  <rect x="680" y="150" width="100" height="80" rx="8" fill="white"/>
                  <rect x="680" y="150" width="100" height="4" rx="2" fill="#EF4444"/>
                  <text x="695" y="175" fill="#64748B" fontSize="12">Absent</text>
                  <text x="695" y="200" fill="#1E293B" fontSize="28" fontWeight="700">5</text>
                  <text x="695" y="220" fill="#EF4444" fontSize="11">2.0%</text>

                  {/* Chart Section */}
                  <rect x="230" y="260" width="350" height="160" rx="8" fill="white"/>
                  <text x="245" y="285" fill="#1E293B" fontSize="16" fontWeight="600">Attendance Trends</text>
                  <text x="245" y="305" fill="#64748B" fontSize="12">Last 7 days</text>

                  {/* Chart */}
                  <polyline points="250,380 280,360 310,370 340,350 370,365 400,345 430,355 460,340 490,350 520,335 550,345"
                            stroke="#3B82F6" strokeWidth="3" fill="none"/>
                  <circle cx="250" cy="380" r="4" fill="#3B82F6"/>
                  <circle cx="310" cy="370" r="4" fill="#3B82F6"/>
                  <circle cx="370" cy="365" r="4" fill="#3B82F6"/>
                  <circle cx="430" cy="355" r="4" fill="#3B82F6"/>
                  <circle cx="490" cy="350" r="4" fill="#3B82F6"/>
                  <circle cx="550" cy="345" r="4" fill="#3B82F6"/>

                  {/* Recent Activity */}
                  <rect x="600" y="260" width="180" height="160" rx="8" fill="white"/>
                  <text x="615" y="285" fill="#1E293B" fontSize="16" fontWeight="600">Recent Activity</text>

                  <circle cx="625" cy="310" r="6" fill="#10B981"/>
                  <text x="640" y="315" fill="#1E293B" fontSize="11">John checked in</text>
                  <text x="640" y="328" fill="#64748B" fontSize="10">2 minutes ago</text>

                  <circle cx="625" cy="350" r="6" fill="#F59E0B"/>
                  <text x="640" y="355" fill="#1E293B" fontSize="11">Leave approved</text>
                  <text x="640" y="368" fill="#64748B" fontSize="10">5 minutes ago</text>

                  <circle cx="625" cy="390" r="6" fill="#3B82F6"/>
                  <text x="640" y="395" fill="#1E293B" fontSize="11">New employee added</text>
                  <text x="640" y="408" fill="#64748B" fontSize="10">1 hour ago</text>
                </svg>
              </div>
            </div>
            {/* Decorative elements */}
            <div className="absolute -bottom-4 -right-4 w-64 h-64 bg-primary-light opacity-10 rounded-full z-0"></div>
            <div className="absolute -top-4 -left-4 w-32 h-32 bg-primary opacity-10 rounded-full z-0"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
