'use client';

import React from 'react';
import { Document } from '@/types/document';
import DocumentCard from './DocumentCard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { FileText, ChevronLeft, ChevronRight } from 'lucide-react';

interface DocumentListProps {
  documents: Document[];
  loading?: boolean;
  onDownload: (document: Document) => void;
  onView: (document: Document) => void;
  onDelete?: (document: Document) => void;
  showEmployeeInfo?: boolean;
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
  onPageChange?: (page: number) => void;
  className?: string;
}

const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  loading = false,
  onDownload,
  onView,
  onDelete,
  showEmployeeInfo = false,
  pagination,
  onPageChange,
  className = ''
}) => {
  if (loading) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-12 ${className}`}>
        <LoadingSpinner size="lg" message="Loading documents..." />
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-12 text-center ${className}`}>
        <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
        <p className="text-gray-500">
          No documents match your current filters. Try adjusting your search criteria or upload your first document.
        </p>
      </div>
    );
  }

  const renderPagination = () => {
    if (!pagination || !onPageChange || pagination.total_pages <= 1) {
      return null;
    }

    const { current_page, total_pages, total_items, items_per_page } = pagination;
    const startItem = (current_page - 1) * items_per_page + 1;
    const endItem = Math.min(current_page * items_per_page, total_items);

    const getPageNumbers = () => {
      const pages = [];
      const maxVisiblePages = 5;
      
      if (total_pages <= maxVisiblePages) {
        for (let i = 1; i <= total_pages; i++) {
          pages.push(i);
        }
      } else {
        const start = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
        const end = Math.min(total_pages, start + maxVisiblePages - 1);
        
        if (start > 1) {
          pages.push(1);
          if (start > 2) pages.push('...');
        }
        
        for (let i = start; i <= end; i++) {
          pages.push(i);
        }
        
        if (end < total_pages) {
          if (end < total_pages - 1) pages.push('...');
          pages.push(total_pages);
        }
      }
      
      return pages;
    };

    return (
      <div className="bg-white px-6 py-4 border-t border-gray-200 flex items-center justify-between">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(current_page - 1)}
            disabled={current_page <= 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => onPageChange(current_page + 1)}
            disabled={current_page >= total_pages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{startItem}</span> to{' '}
              <span className="font-medium">{endItem}</span> of{' '}
              <span className="font-medium">{total_items}</span> results
            </p>
          </div>
          
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(current_page - 1)}
                disabled={current_page <= 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                      ...
                    </span>
                  ) : (
                    <button
                      onClick={() => onPageChange(page as number)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === current_page
                          ? 'z-10 bg-primary border-primary text-white'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  )}
                </React.Fragment>
              ))}
              
              <button
                onClick={() => onPageChange(current_page + 1)}
                disabled={current_page >= total_pages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          Documents ({pagination?.total_items || documents.length})
        </h3>
      </div>

      {/* Document Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {documents.map((document) => (
            <DocumentCard
              key={document.document_id}
              document={document}
              onDownload={onDownload}
              onView={onView}
              onDelete={onDelete}
              showEmployeeInfo={showEmployeeInfo}
            />
          ))}
        </div>
      </div>

      {/* Pagination */}
      {renderPagination()}
    </div>
  );
};

export default DocumentList;
