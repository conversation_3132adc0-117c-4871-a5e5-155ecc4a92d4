"use client";

import React from "react";
import { Document } from "@/types/document";
import { formatFileSize, getFileIcon } from "@/lib/documents";
import {
  Download,
  Eye,
  Trash2,
  Calendar,
  User,
  AlertTriangle,
} from "lucide-react";

interface DocumentCardProps {
  document: Document;
  onDownload: (document: Document) => void;
  onView: (document: Document) => void;
  onDelete?: (document: Document) => void;
  showEmployeeInfo?: boolean;
  className?: string;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  document,
  onDownload,
  onView,
  onDelete,
  showEmployeeInfo = false,
  className = "",
}) => {
  const isExpired = document.is_expired;
  const isExpiringSoon =
    document.days_until_expiry !== undefined &&
    document.days_until_expiry <= 30 &&
    document.days_until_expiry > 0;

  const formatDate = (dateString: string) => {
    const normalized = dateString.includes(" ")
      ? dateString.replace(" ", "T")
      : dateString;

    const date = new Date(normalized);

    if (dateString.includes(" ")) {
      return date.toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      resume: "bg-blue-100 text-blue-800",
      contract: "bg-green-100 text-green-800",
      id_document: "bg-purple-100 text-purple-800",
      certificate: "bg-yellow-100 text-yellow-800",
      policy: "bg-gray-100 text-gray-800",
      training: "bg-indigo-100 text-indigo-800",
      performance: "bg-pink-100 text-pink-800",
      medical: "bg-red-100 text-red-800",
      legal: "bg-orange-100 text-orange-800",
      other: "bg-gray-100 text-gray-800",
    };
    return colors[category] || colors["other"];
  };

  return (
    <div
      className={`bg-white rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-200 overflow-hidden ${className}`}
    >
      {/* Header with expiry warning */}
      {(isExpired || isExpiringSoon) && (
        <div
          className={`px-4 py-2 text-sm font-medium flex items-center ${
            isExpired
              ? "bg-red-50 text-red-700"
              : "bg-yellow-50 text-yellow-700"
          }`}
        >
          <AlertTriangle className="h-4 w-4 mr-2" />
          {isExpired
            ? "Document Expired"
            : `Expires in ${document.days_until_expiry} days`}
        </div>
      )}

      <div className="p-6">
        {/* Document Icon and Basic Info */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-start space-x-3">
            <div className="text-primary">
              {React.createElement(getFileIcon(document.file_type), {
                className: "h-8 w-8",
              })}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {document.document_name}
              </h3>
              <p className="text-sm text-gray-600 truncate">
                {document.file_name}
              </p>
              <p className="text-xs text-gray-500">
                {formatFileSize(document.file_size_bytes)}
              </p>
            </div>
          </div>

          {/* Category Badge */}
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
              document.document_category
            )}`}
          >
            {document.document_category.replace("_", " ").toUpperCase()}
          </span>
        </div>

        {/* Description */}

        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          {document.document_description
            ? document.document_description
            : "No description"}
        </p>

        {/* Metadata */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <Calendar className="h-4 w-4 mr-2" />
            <span>Uploaded: {formatDate(document.uploaded_at)}</span>
          </div>

          {document.expiry_date && (
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Expires: {formatDate(document.expiry_date)}</span>
            </div>
          )}

          {showEmployeeInfo && document.employee_id && (
            <div className="flex items-center text-sm text-gray-500">
              <User className="h-4 w-4 mr-2" />
              <span>Employee Document</span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 border-t border-gray-100 space-y-2 sm:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <button
              onClick={() => onView(document)}
              className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-primary bg-primary-light bg-opacity-10 hover:bg-opacity-20 rounded-md transition-colors touch-manipulation"
            >
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </button>

            <button
              onClick={() => onDownload(document)}
              className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors touch-manipulation"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </button>
          </div>

          {onDelete && (
            <button
              onClick={() => onDelete(document)}
              className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md transition-colors touch-manipulation sm:px-3 sm:py-1.5"
            >
              <Trash2 className="h-4 w-4 sm:mr-0 mr-2" />
              <span className="sm:hidden">Delete</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentCard;
