"use client";

import React, { useState, useRef } from "react";
import {
  DocumentUploadRequest,
  DOCUMENT_CATEGORIES,
  MAX_FILE_SIZE,
} from "@/types/document";
import { validateFile } from "@/lib/documents";
import {
  Upload,
  X,
  FileText,
  AlertCircle,
  Users,
  Building,
} from "lucide-react";

interface DocumentUploadFormProps {
  onSubmit: (data: DocumentUploadRequest) => Promise<void>;
  onCancel?: () => void;
  employees?: Array<{ employee_id: string; full_name: string }>;
  isLoading?: boolean;
  className?: string;
}

const DocumentUploadForm: React.FC<DocumentUploadFormProps> = ({
  onSubmit,
  onCancel,
  employees = [],
  isLoading = false,
  className = "",
}) => {
  const [documentType, setDocumentType] = useState<"general" | "employee">(
    "general"
  );
  const [formData, setFormData] = useState({
    document_name: "",
    document_description: "",
    document_category: "",
    expiry_date: "",
    employee_id: "",
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError("");
  };

  const handleDocumentTypeChange = (type: "general" | "employee") => {
    setDocumentType(type);
    // Clear employee_id when switching to general
    if (type === "general") {
      setFormData((prev) => ({ ...prev, employee_id: "" }));
    }
    setError("");
  };

  const handleFileSelect = (file: File) => {
    const validation = validateFile(file, MAX_FILE_SIZE);
    if (!validation.valid) {
      setError(validation.error || "Invalid file");
      return;
    }

    setSelectedFile(file);
    setError("");

    // Auto-fill document name if empty
    if (!formData.document_name) {
      const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, "");
      setFormData((prev) => ({
        ...prev,
        document_name: nameWithoutExtension,
      }));
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Validation
    if (!formData.document_name.trim()) {
      setError("Document name is required");
      return;
    }

    if (!formData.document_category) {
      setError("Document category is required");
      return;
    }

    if (!selectedFile) {
      setError("Please select a file to upload");
      return;
    }

    // Validate employee selection for employee documents
    if (documentType === "employee" && !formData.employee_id) {
      setError("Please select an employee for employee-specific documents");
      return;
    }

    try {
      const uploadData: DocumentUploadRequest = {
        document_name: formData.document_name.trim(),
        document_description: formData.document_description.trim() || undefined,
        document_category: formData.document_category,
        file: selectedFile,
        expiry_date: formData.expiry_date || undefined,
        employee_id: formData.employee_id || undefined,
      };

      await onSubmit(uploadData);
    } catch (err: any) {
      setError(err.message || "Failed to upload document");
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div
      className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Document Type Selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Document Type *
          </label>
          <div className="grid grid-cols-2 gap-3">
            <button
              type="button"
              onClick={() => handleDocumentTypeChange("general")}
              className={`flex items-center justify-center px-4 py-3 rounded-lg border-2 transition-all ${
                documentType === "general"
                  ? "border-primary bg-primary-light bg-opacity-10 text-primary"
                  : "border-gray-200 hover:border-gray-300 text-gray-700"
              }`}
            >
              <Building className="h-5 w-5 mr-2" />
              <div className="text-left">
                <div className="font-medium">General Document</div>
                <div className="text-xs opacity-75">Company-wide documents</div>
              </div>
            </button>

            <button
              type="button"
              onClick={() => handleDocumentTypeChange("employee")}
              className={`flex items-center justify-center px-4 py-3 rounded-lg border-2 transition-all ${
                documentType === "employee"
                  ? "border-primary bg-primary-light bg-opacity-10 text-primary"
                  : "border-gray-200 hover:border-gray-300 text-gray-700"
              }`}
            >
              <Users className="h-5 w-5 mr-2" />
              <div className="text-left">
                <div className="font-medium">Employee Document</div>
                <div className="text-xs opacity-75">
                  Employee-specific documents
                </div>
              </div>
            </button>
          </div>
        </div>

        {/* File Upload Area */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select File *
          </label>

          <div
            className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-primary bg-primary-light bg-opacity-5"
                : selectedFile
                ? "border-green-300 bg-green-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <FileText className="h-8 w-8 text-green-600" />
                  <div className="text-left">
                    <p className="text-sm font-medium text-gray-900">
                      {selectedFile.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(selectedFile.size)}
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={removeFile}
                  className="text-gray-400 hover:text-red-500 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <div>
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="text-primary hover:text-primary-dark font-medium"
                  >
                    Click to upload
                  </button>
                  <span className="text-gray-500"> or drag and drop</span>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  PDF, Word, Excel, Images, or Text files up to{" "}
                  {Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB
                </p>
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileInputChange}
              accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.txt"
              className="hidden"
            />
          </div>
        </div>

        {/* Document Name */}
        <div>
          <label
            htmlFor="document_name"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Document Name *
          </label>
          <input
            id="document_name"
            name="document_name"
            type="text"
            required
            value={formData.document_name}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter document name"
          />
        </div>

        {/* Document Category */}
        <div>
          <label
            htmlFor="document_category"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Category *
          </label>
          <select
            id="document_category"
            name="document_category"
            required
            value={formData.document_category}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Select a category</option>
            {DOCUMENT_CATEGORIES.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Document Description */}
        <div>
          <label
            htmlFor="document_description"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Description
          </label>
          <textarea
            id="document_description"
            name="document_description"
            rows={3}
            value={formData.document_description}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter document description (optional)"
          />
        </div>

        {/* Employee Selection */}
        {documentType === "employee" && employees.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Users className="h-5 w-5 text-blue-600 mr-2" />
              <label
                htmlFor="employee_id"
                className="block text-sm font-medium text-blue-900"
              >
                Select Employee *
              </label>
            </div>
            <p className="text-sm text-blue-700 mb-3">
              This document will be assigned to the selected employee and can be
              used to track contracts, certificates, and other employee-specific
              documents.
            </p>
            <select
              id="employee_id"
              name="employee_id"
              value={formData.employee_id}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              required={documentType === "employee"}
            >
              <option value="">Select an employee...</option>
              {employees.map((employee) => (
                <option key={employee.employee_id} value={employee.employee_id}>
                  {employee.full_name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Show message when no employees available for employee documents */}
        {documentType === "employee" && employees.length === 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">
                  No Employees Available
                </h4>
                <p className="text-sm text-yellow-700 mt-1">
                  No employees are available for assignment. Please add
                  employees first or switch to General Document type.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Expiry Date */}
        <div
          className={
            documentType === "employee" || documentType === "general"
              ? "bg-orange-50 border border-orange-200 rounded-lg p-4"
              : ""
          }
        >
          {(documentType === "employee" || documentType === "general") && (
            <div className="flex items-center mb-2">
              <AlertCircle className="h-5 w-5 text-orange-600 mr-2" />
              <span className="text-sm font-medium text-orange-900">
                Document Expiry Tracking
              </span>
            </div>
          )}
          <label
            htmlFor="expiry_date"
            className={`block text-sm font-medium mb-1 ${
              documentType === "employee" || documentType === "general"
                ? "text-orange-900"
                : "text-gray-700"
            }`}
          >
            Expiry Date{" "}
            {documentType === "employee" || documentType === "general"
              ? "(Recommended for contracts, Licenses & certificates)"
              : "(Optional)"}
          </label>
          {(documentType === "employee" || documentType === "general") && (
            <p className="text-sm text-orange-700 mb-3">
              Set an expiry date to track contract renewals, certificate
              expirations, and other time-sensitive documents.
            </p>
          )}
          <input
            id="expiry_date"
            name="expiry_date"
            type="date"
            value={formData.expiry_date}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent ${
              documentType === "employee" || documentType === "general"
                ? "border-orange-300 focus:ring-orange-500 bg-white"
                : "border-gray-300 focus:ring-primary"
            }`}
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            disabled={
              isLoading ||
              !selectedFile ||
              (documentType === "employee" && !formData.employee_id)
            }
            className="px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading
              ? "Uploading..."
              : documentType === "employee"
              ? "Upload Employee Document"
              : "Upload General Document"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DocumentUploadForm;
