import React from 'react';

const Benefits = () => {
  return (
    <section id="benefits" className="section bg-background">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            Why Choose KaziSync?
          </h2>
          <p className="text-secondary text-lg">
            Discover how <PERSON><PERSON><PERSON><PERSON> can transform your attendance management and improve operational efficiency.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left Column - Image */}
          <div className="flex items-center justify-center">
            <div className="relative">
              <div className="bg-white rounded-lg shadow-xl p-4 md:p-6 relative z-10">
                <div className="aspect-w-4 aspect-h-3 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex items-center justify-center p-8">
                  {/* Professional Benefits Illustration */}
                  <svg
                    className="w-full h-full max-w-sm max-h-64"
                    viewBox="0 0 400 300"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    {/* Background Elements */}
                    <circle cx="320" cy="60" r="40" fill="#E0F2FE" opacity="0.6"/>
                    <circle cx="80" cy="240" r="30" fill="#DBEAFE" opacity="0.4"/>

                    {/* Main Dashboard Screen */}
                    <rect x="50" y="80" width="300" height="180" rx="12" fill="white" stroke="#E5E7EB" strokeWidth="2"/>

                    {/* Dashboard Header */}
                    <rect x="60" y="90" width="280" height="30" rx="6" fill="#F8FAFC"/>
                    <circle cx="75" cy="105" r="6" fill="#10B981"/>
                    <rect x="90" y="100" width="80" height="10" rx="5" fill="#6B7280"/>
                    <rect x="280" y="100" width="60" height="10" rx="5" fill="#3B82F6"/>

                    {/* Stats Cards */}
                    <rect x="70" y="135" width="60" height="40" rx="6" fill="#EFF6FF"/>
                    <rect x="140" y="135" width="60" height="40" rx="6" fill="#F0FDF4"/>
                    <rect x="210" y="135" width="60" height="40" rx="6" fill="#FEF3F2"/>
                    <rect x="280" y="135" width="60" height="40" rx="6" fill="#FFFBEB"/>

                    {/* Chart Area */}
                    <rect x="70" y="190" width="270" height="60" rx="6" fill="#F8FAFC"/>
                    <polyline points="80,230 120,210 160,220 200,200 240,215 280,195 320,205"
                              stroke="#3B82F6" strokeWidth="3" fill="none"/>
                    <circle cx="80" cy="230" r="3" fill="#3B82F6"/>
                    <circle cx="120" cy="210" r="3" fill="#3B82F6"/>
                    <circle cx="160" cy="220" r="3" fill="#3B82F6"/>
                    <circle cx="200" cy="200" r="3" fill="#3B82F6"/>
                    <circle cx="240" cy="215" r="3" fill="#3B82F6"/>
                    <circle cx="280" cy="195" r="3" fill="#3B82F6"/>
                    <circle cx="320" cy="205" r="3" fill="#3B82F6"/>

                    {/* Floating Elements */}
                    <g transform="translate(360, 120)">
                      <circle r="25" fill="white" stroke="#E5E7EB" strokeWidth="2"/>
                      <path d="M-8 -4 L-2 2 L8 -8" stroke="#10B981" strokeWidth="2" fill="none" strokeLinecap="round"/>
                    </g>

                    <g transform="translate(20, 160)">
                      <circle r="20" fill="white" stroke="#E5E7EB" strokeWidth="2"/>
                      <rect x="-6" y="-6" width="12" height="12" rx="2" fill="#3B82F6"/>
                    </g>

                    {/* People Icons */}
                    <g transform="translate(100, 40)">
                      <circle cx="0" cy="0" r="12" fill="#3B82F6"/>
                      <circle cx="20" cy="0" r="12" fill="#10B981"/>
                      <circle cx="40" cy="0" r="12" fill="#F59E0B"/>
                    </g>
                  </svg>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -bottom-4 -left-4 w-48 h-48 bg-primary-light opacity-10 rounded-full z-0"></div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary opacity-10 rounded-full z-0"></div>
            </div>
          </div>

          {/* Right Column - Benefits List */}
          <div className="space-y-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Increased Productivity
                </h3>
                <p className="text-secondary">
                  Automate attendance tracking and leave management processes, freeing up HR resources for more strategic tasks.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Enhanced Accuracy
                </h3>
                <p className="text-secondary">
                  Eliminate manual errors and ensure precise attendance records with biometric.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Real-time Insights
                </h3>
                <p className="text-secondary">
                  Access up-to-date attendance data and analytics to make informed decisions about workforce management.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Improved Compliance
                </h3>
                <p className="text-secondary">
                  Maintain accurate records of working hours and leaves to ensure compliance with labor regulations.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Cost Efficiency
                </h3>
                <p className="text-secondary">
                  Reduce administrative overhead and minimize time theft with accurate attendance tracking.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;
