'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { getAvailableTemplates, formatDaysOfWeek, getWeekendRequirementText } from '@/lib/advanced-shift-api';
import { TemplateWithSummary } from '@/types/advanced-shift';
import { Calendar, Clock, BarChart3, Plus, ChevronDown } from 'lucide-react';

interface TemplateManagementProps {
  onCreateNew?: () => void;
  onAnalyzeTemplate?: (template: TemplateWithSummary) => void;
  onGenerateSchedule?: (template: TemplateWithSummary) => void;
}

const TemplateManagement: React.FC<TemplateManagementProps> = ({
  onCreateNew,
  onAnalyzeTemplate,
  onGenerateSchedule
}) => {
  const { companies } = useAuth();
  const [templates, setTemplates] = useState<TemplateWithSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'weekly' | 'monthly' | 'custom'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'created_at' | 'coverage_hours'>('created_at');
  const [expandedTemplate, setExpandedTemplate] = useState<string | null>(null);

  useEffect(() => {
    if (companies.length > 0) {
      fetchTemplates();
    }
  }, [companies]);

  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      setError('');
      const companyId = companies[0].company_id;
      const response = await getAvailableTemplates(companyId);
      setTemplates(response.templates);
    } catch (error: any) {
      setError(error.message || 'Failed to fetch templates');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredAndSortedTemplates = templates
    .filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filterBy === 'all' || template.pattern_type === filterBy;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created_at':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'coverage_hours':
          return b.weekly_coverage_hours - a.weekly_coverage_hours;
        default:
          return 0;
      }
    });

  const toggleExpanded = (templateId: string) => {
    setExpandedTemplate(expandedTemplate === templateId ? null : templateId);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPatternTypeColor = (patternType: string) => {
    switch (patternType) {
      case 'weekly':
        return 'bg-green-100 text-green-800';
      case 'monthly':
        return 'bg-blue-100 text-blue-800';
      case 'custom':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <DashboardCard title="Template Management">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-secondary">Loading templates...</span>
        </div>
      </DashboardCard>
    );
  }

  if (error) {
    return (
      <DashboardCard title="Template Management">
        <div className="text-center py-8">
          <div className="text-red-600 mb-4">⚠️ {error}</div>
          <button
            onClick={fetchTemplates}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            Retry
          </button>
        </div>
      </DashboardCard>
    );
  }

  return (
    <DashboardCard title="Scheduling Templates">
      <div className="p-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="text-sm text-secondary">
              {templates.length} template{templates.length !== 1 ? 's' : ''} available
            </div>
            {onCreateNew && (
              <button
                onClick={onCreateNew}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Create New Template
              </button>
            )}
          </div>
        </div>

        {/* Filters and Search */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-secondary-dark mb-2">
              Search Templates
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Search by name or description..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-dark mb-2">
              Filter by Type
            </label>
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="custom">Custom</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-dark mb-2">
              Sort by
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="created_at">Created Date</option>
              <option value="name">Name</option>
              <option value="coverage_hours">Coverage Hours</option>
            </select>
          </div>
        </div>

        {/* Templates List */}
        {filteredAndSortedTemplates.length === 0 ? (
          <div className="text-center py-8">
            {searchTerm || filterBy !== 'all' ? (
              <div>
                <p className="text-secondary mb-4">No templates match your search criteria.</p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilterBy('all');
                  }}
                  className="text-primary hover:text-primary-dark"
                >
                  Clear filters
                </button>
              </div>
            ) : (
              <div>
                <p className="text-secondary mb-4">No scheduling templates found.</p>
                {onCreateNew && (
                  <button
                    onClick={onCreateNew}
                    className="text-primary hover:text-primary-dark"
                  >
                    Create your first template
                  </button>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedTemplates.map((template) => (
              <div
                key={template.template_id}
                className="border rounded-lg overflow-hidden hover:border-gray-300 transition-colors"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 pr-6">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-lg font-semibold text-secondary-dark">{template.name}</h3>
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${getPatternTypeColor(template.pattern_type)}`}>
                          {template.pattern_type}
                        </span>
                        {template.is_24_7_coverage && (
                          <span className="px-3 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                            24/7
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-secondary mb-4 leading-relaxed">{template.description}</p>
                      <div className="flex items-center space-x-6 text-sm text-secondary">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(template.created_at)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4" />
                          <span>{template.staffing_summary.total_shifts} shift{template.staffing_summary.total_shifts !== 1 ? 's' : ''}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <BarChart3 className="w-4 h-4" />
                          <span>{template.weekly_coverage_hours}h/week</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end space-y-3">
                      <div className="flex items-center space-x-3">
                        {onAnalyzeTemplate && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onAnalyzeTemplate(template);
                            }}
                            className="px-4 py-2 text-sm font-medium bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors flex items-center space-x-2"
                          >
                            <BarChart3 className="w-4 h-4" />
                            <span>Analyze</span>
                          </button>
                        )}
                        {onGenerateSchedule && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onGenerateSchedule(template);
                            }}
                            className="px-4 py-2 text-sm font-medium bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors flex items-center space-x-2"
                          >
                            <Plus className="w-4 h-4" />
                            <span>Generate</span>
                          </button>
                        )}
                      </div>
                      <button
                        onClick={() => toggleExpanded(template.template_id)}
                        className="flex items-center space-x-2 text-sm text-secondary hover:text-secondary-dark transition-colors"
                      >
                        <span>{expandedTemplate === template.template_id ? 'Hide Details' : 'Show Details'}</span>
                        <ChevronDown
                          className={`w-4 h-4 transition-transform ${expandedTemplate === template.template_id ? 'rotate-180' : ''}`}
                        />
                      </button>
                    </div>
                  </div>
                </div>

                {expandedTemplate === template.template_id && (
                  <div className="border-t bg-gray-50 p-4">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Staffing Summary */}
                      <div>
                        <h4 className="font-medium text-secondary-dark mb-3">Weekly Staffing Summary</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Min Staff Hours:</span>
                            <span className="font-medium">{template.staffing_summary.weekly_totals.min_staff_hours}h</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Preferred Staff Hours:</span>
                            <span className="font-medium">{template.staffing_summary.weekly_totals.preferred_staff_hours}h</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Max Staff Hours:</span>
                            <span className="font-medium">{template.staffing_summary.weekly_totals.max_staff_hours}h</span>
                          </div>
                        </div>
                      </div>

                      {/* Work Rules Summary */}
                      <div>
                        <h4 className="font-medium text-secondary-dark mb-3">Work Rules</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Max Consecutive Days:</span>
                            <span className="font-medium">{template.work_rules_summary.max_consecutive_days}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Max Hours/Week:</span>
                            <span className="font-medium">{template.work_rules_summary.max_hours_per_week}h</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Min Rest Days:</span>
                            <span className="font-medium">{template.work_rules_summary.min_rest_days}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Overtime:</span>
                            <span className="font-medium">{template.work_rules_summary.overtime_allowed ? 'Allowed' : 'Not Allowed'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Weekend:</span>
                            <span className="font-medium">{getWeekendRequirementText(template.work_rules_summary.weekend_requirements)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Daily Staffing Breakdown */}
                    <div className="mt-6">
                      <h4 className="font-medium text-secondary-dark mb-3">Daily Staffing Requirements</h4>
                      <div className="grid grid-cols-7 gap-2">
                        {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                          const dayNum = (index + 1).toString();
                          const dayData = template.staffing_summary.daily_totals[dayNum];
                          return (
                            <div key={day} className="text-center p-2 bg-white rounded border">
                              <div className="text-xs font-medium text-secondary-dark mb-1">{day}</div>
                              <div className="text-xs space-y-1">
                                <div>Min: {dayData.min_staff}</div>
                                <div>Pref: {dayData.preferred_staff}</div>
                                <div>Max: {dayData.max_staff}</div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </DashboardCard>
  );
};

export default TemplateManagement;
