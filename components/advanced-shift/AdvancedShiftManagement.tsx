'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import DashboardCard from '@/components/ui/DashboardCard';
import TemplateExamples from './TemplateExamples';
import TemplateBuilder from './TemplateBuilder';
import TemplateManagement from './TemplateManagement';
import TemplateAnalysisComponent from './TemplateAnalysis';
import ScheduleGeneration from './ScheduleGeneration';
import ScheduleAssignment from './ScheduleAssignment';
import {
  TemplateExample,
  TemplateWithSummary,
  GeneratedSchedule,
  AssignScheduleResponse
} from '@/types/advanced-shift';
import { ClipboardList, Settings, Sparkles } from 'lucide-react';

type ViewType = 
  | 'overview' 
  | 'examples' 
  | 'templates' 
  | 'create-template' 
  | 'analyze-template' 
  | 'generate-schedule' 
  | 'assign-schedule';

interface ViewState {
  view: ViewType;
  data?: any;
}

const AdvancedShiftManagement: React.FC = () => {
  const [viewState, setViewState] = useState<ViewState>({ view: 'overview' });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const navigateTo = (view: ViewType, data?: any) => {
    setViewState({ view, data });
  };

  const handleTemplateCreated = () => {
    setRefreshTrigger(prev => prev + 1);
    navigateTo('templates');
  };

  const handleUseTemplate = (example: TemplateExample, exampleKey: string) => {
    navigateTo('create-template', example);
  };

  const handleAnalyzeTemplate = (template: TemplateWithSummary) => {
    navigateTo('analyze-template', template);
  };

  const handleGenerateSchedule = (template: TemplateWithSummary) => {
    navigateTo('generate-schedule', template);
  };

  const handleScheduleGenerated = (schedule: GeneratedSchedule) => {
    navigateTo('assign-schedule', schedule);
  };

  const handleAssignmentComplete = (result: AssignScheduleResponse) => {
    // Could navigate to a success page or back to overview
    navigateTo('overview');
  };

  const renderBreadcrumb = () => {
    const breadcrumbItems = [];

    // Always start with Dashboard and Shifts
    breadcrumbItems.push(
      { label: 'Dashboard', href: '/dashboard/hr' },
      { label: 'Shifts', href: '/dashboard/hr/shifts' }
    );

    switch (viewState.view) {
      case 'overview':
        breadcrumbItems.push({ label: 'Advanced Scheduling', active: true });
        break;
      case 'examples':
        breadcrumbItems.push(
          { label: 'Advanced Scheduling', onClick: () => navigateTo('overview') },
          { label: 'Template Examples', active: true }
        );
        break;
      case 'templates':
        breadcrumbItems.push(
          { label: 'Advanced Scheduling', onClick: () => navigateTo('overview') },
          { label: 'Manage Templates', active: true }
        );
        break;
      case 'create-template':
        breadcrumbItems.push(
          { label: 'Advanced Scheduling', onClick: () => navigateTo('overview') },
          { label: 'Manage Templates', onClick: () => navigateTo('templates') },
          { label: 'Create Template', active: true }
        );
        break;
      case 'analyze-template':
        breadcrumbItems.push(
          { label: 'Advanced Scheduling', onClick: () => navigateTo('overview') },
          { label: 'Manage Templates', onClick: () => navigateTo('templates') },
          { label: 'Analyze Template', active: true }
        );
        break;
      case 'generate-schedule':
        breadcrumbItems.push(
          { label: 'Advanced Scheduling', onClick: () => navigateTo('overview') },
          { label: 'Generate Schedule', active: true }
        );
        break;
      case 'assign-schedule':
        breadcrumbItems.push(
          { label: 'Advanced Scheduling', onClick: () => navigateTo('overview') },
          { label: 'Assign Schedule', active: true }
        );
        break;
    }

    return (
      <nav className="flex mb-6" aria-label="Breadcrumb">
        <ol className="flex items-center space-x-2">
          {breadcrumbItems.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && <span className="mx-2 text-gray-400">/</span>}
              {item.active ? (
                <span className="text-secondary-dark font-medium">{item.label}</span>
              ) : item.href ? (
                <Link
                  href={item.href}
                  className="text-primary hover:text-primary-dark transition-colors"
                >
                  {item.label}
                </Link>
              ) : (
                <button
                  onClick={item.onClick}
                  className="text-primary hover:text-primary-dark transition-colors"
                >
                  {item.label}
                </button>
              )}
            </li>
          ))}
        </ol>
      </nav>
    );
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <DashboardCard title="Advanced Shift Management">
        <div className="p-6">
          <p className="text-secondary mb-6">
            Create sophisticated scheduling templates with business rules, generate schedules automatically, 
            and assign them to employees or departments with intelligent algorithms.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Template Examples */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                  <ClipboardList className="text-white h-6 w-6" />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-blue-900">Template Examples</h3>
              </div>
              <p className="text-blue-800 text-sm mb-4">
                Explore pre-built templates for hospitals, manufacturing, and office environments.
              </p>
              <button
                onClick={() => navigateTo('examples')}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                View Examples
              </button>
            </div>

            {/* Template Management */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                  <Settings className="text-white h-6 w-6" />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-green-900">Manage Templates</h3>
              </div>
              <p className="text-green-800 text-sm mb-4">
                Create, edit, and manage your scheduling templates with advanced business rules.
              </p>
              <button
                onClick={() => navigateTo('templates')}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Manage Templates
              </button>
            </div>

            {/* Create Template */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="text-white h-6 w-6" />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-purple-900">Create Template</h3>
              </div>
              <p className="text-purple-800 text-sm mb-4">
                Build a new scheduling template from scratch with custom business requirements.
              </p>
              <button
                onClick={() => navigateTo('create-template')}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Create New
              </button>
            </div>
          </div>
        </div>
      </DashboardCard>

      {/* Features Overview */}
      <DashboardCard title="Key Features">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-secondary-dark mb-3">Business Rules Engine</h4>
              <ul className="space-y-2 text-sm text-secondary">
                <li>• Maximum consecutive working days</li>
                <li>• Minimum rest periods between shifts</li>
                <li>• Overtime policies and restrictions</li>
                <li>• Weekend coverage requirements</li>
                <li>• Employee preference considerations</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-secondary-dark mb-3">Advanced Scheduling</h4>
              <ul className="space-y-2 text-sm text-secondary">
                <li>• 24/7 coverage planning</li>
                <li>• Role-based staffing requirements</li>
                <li>• Automatic schedule generation</li>
                <li>• Department-wide assignments</li>
                <li>• Validation and conflict detection</li>
              </ul>
            </div>
          </div>
        </div>
      </DashboardCard>
    </div>
  );

  return (
    <div className="space-y-6">
      {renderBreadcrumb()}
      
      {viewState.view === 'overview' && renderOverview()}
      
      {viewState.view === 'examples' && (
        <TemplateExamples onUseTemplate={handleUseTemplate} />
      )}
      
      {viewState.view === 'templates' && (
        <TemplateManagement
          key={refreshTrigger}
          onCreateNew={() => navigateTo('create-template')}
          onAnalyzeTemplate={handleAnalyzeTemplate}
          onGenerateSchedule={handleGenerateSchedule}
        />
      )}
      
      {viewState.view === 'create-template' && (
        <TemplateBuilder
          prefilledData={viewState.data}
          onSuccess={handleTemplateCreated}
          onCancel={() => navigateTo('templates')}
        />
      )}
      
      {viewState.view === 'analyze-template' && viewState.data && (
        <TemplateAnalysisComponent
          template={viewState.data}
          onClose={() => navigateTo('templates')}
          onGenerateSchedule={handleGenerateSchedule}
        />
      )}
      
      {viewState.view === 'generate-schedule' && viewState.data && (
        <ScheduleGeneration
          template={viewState.data}
          onSuccess={handleScheduleGenerated}
          onCancel={() => navigateTo('templates')}
        />
      )}
      
      {viewState.view === 'assign-schedule' && viewState.data && (
        <ScheduleAssignment
          schedule={viewState.data}
          onSuccess={handleAssignmentComplete}
          onCancel={() => navigateTo('overview')}
        />
      )}
    </div>
  );
};

export default AdvancedShiftManagement;
