{"name": "kazi-sync", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next", "prebuild": "npm run clean", "render-build": "npm install --production=false && npm run build"}, "dependencies": {"@tailwindcss/line-clamp": "^0.4.4", "@types/react-datepicker": "^6.2.0", "autoprefixer": "^10.4.16", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "lucide-react": "^0.542.0", "next": "^15.3.3", "postcss": "^8.4.32", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "sonner": "^2.0.4", "tailwindcss": "^3.4.0", "xlsx": "https://cdn.sheetjs.com/xlsx-latest/xlsx-latest.tgz"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/html2canvas": "^0.5.35", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/xlsx": "^0.0.35", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "typescript": "^5.3.3"}}